apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion  rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        resConfigs rootProject.ext.android.resConfigs
        versionCode 1
        versionName "1.0.1"
    }
    signingConfigs {
        ybm {
            keyAlias '小药药'
            keyPassword 'ybmmarket20'
            storeFile file('ybm.jks')
            storePassword 'ybmmarket20'
        }
    }
    buildTypes {
        release {//线上版本
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
            signingConfig signingConfigs.ybm
            debuggable false
            jniDebuggable false
        }
    }

    lintOptions {
        abortOnError Boolean.valueOf(modulesLintAbortOnError)
        ignoreWarnings Boolean.valueOf(modulesLintIgnoreWarnings)
        if (Boolean.valueOf(modulesLintBaseLineEnable)) {
            baseline file("lint-baseline.xml")
        }
        lintConfig file("$rootDir/lint.xml")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/lite-go-1.0.0.jar')

    api 'com.xyyio.analysis:xyyio:1.4.0'
    api 'com.xyy.apm:apmcly:1.0.0'

    implementation deps.appcompatV7
    implementation deps.supportV4
    implementation deps.recyclerview
    implementation deps.bugly
    implementation deps.okhttp
    api deps.gson
    implementation deps.adapterhelper
    implementation deps.logutils
    implementation deps.glide
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.0'
    api "io.reactivex.rxjava2:rxjava:2.2.6"
    api "com.squareup.retrofit2:adapter-rxjava2:2.4.0"
    api "io.reactivex.rxjava2:rxandroid:2.1.1"
    api('com.tbruyelle.rxpermissions2:rxpermissions:0.9.5', {
        exclude module: 'rxjava'
        exclude group: 'com.android.support'
    })
    api project(path: ':waf')
    implementation project(path: ':common')
    api 'com.tencent.tdos-diagnose:diagnose:0.4.11'
    api 'com.tencent.tdos-diagnose:logger:0.4.11'
}
