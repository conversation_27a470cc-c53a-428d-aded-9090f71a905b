<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical"
   >

    <LinearLayout android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:id="@+id/ll_item"
                  android:layout_gravity="center_vertical"
                  android:background="@drawable/line_bg_1px_bottom"
                  android:orientation="horizontal"
                  android:padding="@dimen/dimen_dp_15" >

    <TextView
        android:id="@+id/tv_icon_type_01"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_brand_item_type2"
        android:paddingLeft="2dp"
        android:paddingRight="2dp"
        android:text="满减"
        android:textColor="@color/white"
        android:textSize="11sp" />

    <ImageView
        android:id="@+id/iv_icon_type_01"
        style="@style/detail_promotion_iv"
        android:layout_gravity="center_vertical" />

    <TextView
        android:id="@+id/tv_content_type_01"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="11dp"
        android:layout_marginRight="11dp"
        android:layout_weight="1"
        android:textColor="@color/text_292933"
        android:textSize="14sp"
        tools:text="满10元另加9.90元即赠热销商品，赠完即止， 请在购物车点击领取" />

    <ImageView
        android:id="@+id/iv"
        android:layout_width="@dimen/dimen_dp_15"
        android:layout_height="@dimen/dimen_dp_15"
        android:background="@drawable/right_new" />
    </LinearLayout>

    <com.ybmmarket20.view.CSUListView android:layout_width="match_parent"
                                      android:layout_height="wrap_content"
                                      android:layout_marginLeft="10dp"
                                      android:layout_marginRight="10dp"
                                      android:background="@color/transparent"
                                      android:id="@+id/csu_list" />
</LinearLayout>