<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_top_triangle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_think_triangle"
        app:layout_constraintHorizontal_bias="0.7815533981"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:layout_width="219dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_top_triangle"
        android:paddingLeft="15dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_top_triangle"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="6dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <TextView
                android:id="@+id/tv_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_292933"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="被委托人信息确认失败" />

            <ImageView
                android:id="@+id/bt_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="10dp"
                android:paddingTop="3dp"
                android:paddingRight="10dp"
                android:paddingBottom="3dp"
                android:src="@drawable/close_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_des"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </com.ybmmarket20.common.widget.RoundConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>