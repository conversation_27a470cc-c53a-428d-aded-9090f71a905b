<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="270dp"
        android:layout_gravity="center_horizontal"
        android:layout_height="wrap_content"
        android:elevation="@dimen/dimen_dp_0">
        <LinearLayout
            android:id="@+id/llBg"
            android:layout_width="270dp"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"
            android:layout_gravity="center"
            android:background="@drawable/my_dialog_bg"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical"
                android:text="标题"
                android:layout_marginTop="10dp"
                android:textColor="@color/color_292933"
                android:textSize="17sp"
                android:textStyle="bold" />
            <com.ybmmarket20.view.MaxHeightView
                android:id="@+id/maxHeightView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <Space
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dimen_dp_10" />
                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/li_dialog_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="fill_horizontal"
                        android:orientation="vertical"
                        android:paddingLeft="25dp"
                        android:paddingRight="25dp">
                        <TextView
                            android:id="@+id/tv_dialog_message"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:scrollbars="vertical"
                            android:text="提示消息"
                            android:textColor="@color/color_292933"
                            android:textSize="14sp" />
                    </LinearLayout>
                </ScrollView>
            </com.ybmmarket20.view.MaxHeightView>


            <Space
                android:id="@+id/bottomSpace"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dimen_dp_10"
                android:visibility="gone"/>

            <LinearLayout
                android:id="@+id/li_dialog_buttons_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="50dp"
                android:orientation="vertical"
                android:paddingTop="20dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colors_DDDDDD" />

                <LinearLayout
                    android:id="@+id/li_dialog_buttons"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="35dp"
                    android:background="@color/white"
                    android:orientation="horizontal"></LinearLayout>

                <LinearLayout
                    android:id="@+id/li_dialog_buttons_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="35dp"
                    android:visibility="gone"
                    android:orientation="vertical" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>


</LinearLayout>