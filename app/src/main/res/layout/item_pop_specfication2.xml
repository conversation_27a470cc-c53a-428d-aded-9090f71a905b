<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="@dimen/dimen_dp_10">


    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_selecter_seach_spacfication"
        android:button="@null"
        android:clickable="false"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:textColor="@color/selector_text_color_292933"
        android:padding="@dimen/dimen_dp_10"
        tools:checked="true"
        tools:text="15mg*10" />

</LinearLayout>