<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <View
        android:id="@+id/view_bg"
        android:layout_width="60dp"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <include
            layout="@layout/base_pop_filtrate_class_common_title"
            android:visibility="invisible" />

        <com.ybmmarket20.view.MyScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_ybm"
                    style="@style/filtrate_classify_pop_tv_02"
                    android:layout_marginTop="18dp"
                    android:text="药帮忙服务"
                    android:textSize="13.5sp" />

                <LinearLayout
                    android:id="@+id/ll_ybm"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingLeft="9dp">

                    <TextView
                        android:id="@+id/tv_available"
                        style="@style/filtrate_classify_pop_tv"
                        android:text="仅看有货" />

                    <TextView
                        android:id="@+id/tv_promotion"
                        style="@style/filtrate_classify_pop_tv"
                        android:layout_marginLeft="9dp"
                        android:text="有促销" />

                </LinearLayout>

                <TextView
                        android:id="@+id/shop_service_title"
                        style="@style/filtrate_classify_pop_tv_02"
                        android:layout_marginTop="18dp"
                        android:text="店铺服务"
                        android:textSize="13.5sp" />

                <LinearLayout
                        android:id="@+id/shop_service_options"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="horizontal"
                        android:paddingLeft="9dp">

                    <TextView
                            android:id="@+id/express_shunfeng"
                            style="@style/filtrate_classify_pop_tv"
                            android:text="顺丰快递" />

                    <TextView
                            android:id="@+id/express_jd"
                            style="@style/filtrate_classify_pop_tv"
                            android:layout_marginLeft="9dp"
                            android:text="京东快递" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_price_range"
                    style="@style/filtrate_classify_pop_tv_02"
                    android:layout_marginTop="18dp"
                    android:text="价格区间"
                    android:textSize="13.5sp" />

                <LinearLayout
                    android:id="@+id/ll_price_range"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:orientation="horizontal"
                    android:paddingLeft="9dp"
                    android:paddingRight="9dp">

                    <FrameLayout style="@style/filtrate_classify_pop_fl">

                        <TextView
                            android:id="@+id/tv_price_range_floor"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@null"
                            android:gravity="center"
                            android:text="最低价"
                            android:textColor="#c0c0c0"
                            android:textSize="13sp" />

                        <EditText
                            android:id="@+id/price_range_floor"
                            style="@style/filtrate_classify_pop_et" />

                    </FrameLayout>

                    <View
                        android:id="@+id/icon_section"
                        android:layout_width="13dp"
                        android:layout_height="1dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="8dp"
                        android:layout_marginRight="8dp"
                        android:background="@drawable/line_bg_1px_filtrate_classify" />

                    <FrameLayout style="@style/filtrate_classify_pop_fl">

                        <TextView
                            android:id="@+id/tv_price_range_top"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@null"
                            android:gravity="center"
                            android:text="最高价"
                            android:textColor="#c0c0c0"
                            android:textSize="13sp" />

                        <EditText
                            android:id="@+id/price_range_top"
                            style="@style/filtrate_classify_pop_et" />

                    </FrameLayout>

                </LinearLayout>

                <TextView
                    style="@style/filtrate_classify_pop_tv_02"
                    android:layout_marginTop="18dp"
                    android:text="药品类型"
                    android:textSize="13.5sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="9dp"
                    android:paddingRight="9dp">

                    <TextView
                        android:id="@+id/class_a"
                        style="@style/filtrate_classify_pop_tv_01"
                        android:text="甲类OTC" />

                    <TextView
                        android:id="@+id/class_b"
                        style="@style/filtrate_classify_pop_tv_01"
                        android:layout_marginLeft="9dp"
                        android:text="乙类OTC" />

                    <TextView
                        android:id="@+id/class_rx"
                        style="@style/filtrate_classify_pop_tv_01"
                        android:layout_marginLeft="9dp"
                        android:text="处方药RX" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingLeft="9dp"
                    android:paddingRight="9dp">

                    <TextView
                        android:id="@+id/class_else"
                        style="@style/filtrate_classify_pop_tv_05"
                        android:text="其他" />

                    <Space
                        style="@style/filtrate_classify_pop_tv_05"
                        android:layout_marginLeft="9dp"
                        android:visibility="invisible" />

                    <Space
                        style="@style/filtrate_classify_pop_tv_05"
                        android:layout_marginLeft="9dp"
                        android:visibility="invisible" />

                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/rl_all_manufacture"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="13dp"
                    android:paddingLeft="@dimen/filtrate_classify_padding_left"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp">

                    <TextView
                        android:id="@+id/tv_all_manufacture"
                        style="@style/filtrate_classify_pop_tv_03"
                        android:text="生产厂商"
                        android:textSize="13.5sp" />

                    <ImageView
                        android:id="@+id/iv_right_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/dimen_dp_10"
                        android:src="@drawable/icon_right_gray" />

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/ll_manufacture_selected"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/ll_01"
                        style="@style/filtrate_classify_pop_ll_01"
                        tools:visibility="gone">

                        <TextView
                            android:id="@+id/tv_details_01"
                            style="@style/filtrate_classify_pop_tv_04" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_02"
                        style="@style/filtrate_classify_pop_ll_01"
                        tools:visibility="gone">

                        <TextView
                            android:id="@+id/tv_details_02"
                            style="@style/filtrate_classify_pop_tv_04" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_03"
                        style="@style/filtrate_classify_pop_ll_01"
                        tools:visibility="gone">

                        <TextView
                            android:id="@+id/tv_details_03"
                            style="@style/filtrate_classify_pop_tv_04" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_04"
                        style="@style/filtrate_classify_pop_ll_01">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="5dp"
                            android:paddingTop="5dp"
                            android:paddingRight="5dp"
                            android:paddingBottom="3dp">

                            <ImageView
                                android:id="@+id/iv_details_04"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:src="@drawable/icon_detail_service_omit" />

                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.ybmmarket20.view.MyScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:gravity="bottom"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_reset"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/bg_filtrate_classify_btn2_reset"
                android:text="重置"
                android:textColor="@color/color_292933"
                android:textSize="16sp" />

            <Button
                android:id="@+id/btn_affirm"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/detail_tv_00B377"
                android:elevation="0dp"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>