<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:layout_weight="8"
    android:padding="10dp">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="15dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:text="添加图片"
            android:textColor="#8C8C8C"
            android:textSize="13sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider"
            android:background="@color/divider_line_base_1px" />

        <TextView
            android:id="@+id/mSelectPicTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:text="相册选择"
            android:textColor="@color/text_676773"
            android:textSize="17sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider"
            android:background="@color/divider_line_base_1px" />

        <TextView
            android:id="@+id/mTakePicTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:text="拍照"
            android:textColor="@color/text_676773"
            android:textSize="17sp" />
    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/mCancelTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:text="退出"
        android:textColor="@color/text_676773"
        android:textSize="17sp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="15dp" />


</LinearLayout>