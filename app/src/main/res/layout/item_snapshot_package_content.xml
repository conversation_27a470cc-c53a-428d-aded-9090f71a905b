<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/snapshotIv"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/snapshotTitleLl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="8dp"
        android:layout_marginRight="8dp"
        android:orientation="horizontal"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/snapshotIv"
        app:layout_constraintTop_toTopOf="@id/snapshotIv">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/snapshotActivityIv"
            android:layout_width="42dp"
            android:layout_height="17dp"
            android:layout_marginRight="5dp"
            android:background="@drawable/icon_procurement_festival"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/snapshotNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#ff292933"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
    <!--效-->
    <LinearLayout
        android:id="@+id/ll_validity_period"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="@id/snapshotTitleLl"
        app:layout_constraintTop_toBottomOf="@+id/snapshotTitleLl"
        android:visibility="gone"
        tools:visibility="visible">

        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:gravity="center"
            android:includeFontPadding="true"
            android:text="效"
            android:textColor="@color/tag_head_text"
            android:textSize="10sp"
            app:rv_backgroundColor="@color/tag_head_background"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/tag_head_stroke"
            app:rv_strokeWidth="1dp" />

        <TextView
            android:id="@+id/tv_validity_period"
            style="@style/goods_list_item_text_small"
            android:layout_marginLeft="4dp"
            tools:text="xxxxxxxx" />
    </LinearLayout>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/snapshotPriceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="@string/snapshot_package_price"
        android:textColor="#ff676773"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="@id/snapshotTitleLl"
        app:layout_constraintTop_toBottomOf="@+id/ll_validity_period" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/snapshotSpecTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_10"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="@string/snapshot_spec"
        android:textColor="#ff676773"
        android:textSize="12sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/snapshotPriceTv"
        app:layout_constraintEnd_toStartOf="@id/snapshotNumTv"
        app:layout_constraintStart_toEndOf="@+id/snapshotPriceTv"
        app:layout_constraintTop_toTopOf="@+id/snapshotPriceTv" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/snapshotNumTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:text="@string/trading_snapshot_goods_num"
        android:textColor="#ff676773"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/snapshotSpecTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/snapshotSpecTv" />

    <com.ybmmarket20.view.TradingSnapshotTagView
        android:id="@+id/activityTagView"
        android:layout_width="wrap_content"
        android:layout_height="15dp"
        android:layout_marginTop="6dp"
        app:layout_constraintStart_toStartOf="@+id/snapshotTitleLl"
        app:layout_constraintTop_toBottomOf="@+id/snapshotPriceTv" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/snapshotTotalTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snapshot_package_total"
        android:textColor="#ffff2121"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="@+id/snapshotPriceTv"
        app:layout_constraintTop_toBottomOf="@id/activityTagView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/goSnapshotDetail"
        android:layout_width="88dp"
        android:layout_height="32dp"
        android:layout_marginRight="@dimen/dp_10"
        android:background="@drawable/shape_snapshot_btn"
        android:gravity="center"
        android:text="@string/trading_snapshot"
        android:textColor="#ff00b377"
        app:layout_constraintBottom_toBottomOf="@+id/snapshotIv"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>