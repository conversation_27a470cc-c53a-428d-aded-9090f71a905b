<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvShopName"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:drawableStart="@drawable/icon_shop_title"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:textStyle="bold"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        tools:text="江西明鑫健康产业集团有限公司" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:layout_marginTop="@dimen/dimen_dp_23"
        android:text="企业相关资质"
        android:textStyle="bold"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14" />

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/flLicense"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:flexWrap="wrap"
        tools:layout_height="@dimen/dimen_dp_50" />
</LinearLayout>