<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F7F7"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_order_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintTop_toBottomOf="@+id/header">

        <TextView
            android:id="@+id/tv_order_no_title"
            style="@style/InvoicePopTitle"
            android:text="订单编号:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_order_no"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_order_no_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="ybm10018028080808" />

        <View
            android:id="@+id/tv_order_no_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/color_f7f7f8"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_title" />

        <TextView
            android:id="@+id/tv_order_time_title"
            style="@style/InvoicePopTitle"
            android:text="下单时间:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_line" />

        <TextView
            android:id="@+id/tv_order_time"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_order_time_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_no_line"
            tools:text="2021/03/12 11:23:34" />

        <View
            android:id="@+id/tv_order_time_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/color_f7f7f8"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_title" />

        <TextView
            android:id="@+id/tv_receive_time_title"
            style="@style/InvoicePopTitle"
            android:text="支付时间:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_line" />

        <TextView
            android:id="@+id/tv_receive_time"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_receive_time_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_order_time_line"
            tools:text="2021/03/12 11:23:34" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_invoice_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintTop_toBottomOf="@+id/tv_receive_time_line"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_type_title"
            style="@style/InvoicePopTitle"
            android:text="发票类型:"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_type"
            style="@style/InvoicePopText"
            android:layout_marginStart="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_type_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="电子普通发票" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg" />

</LinearLayout>