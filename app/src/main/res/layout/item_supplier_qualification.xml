<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:padding="@dimen/normal_margin"
              android:background="#EFEFEF">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius_TL="6.67dp"
            app:rv_cornerRadius_TR="6.67dp"
            android:padding="@dimen/normal_margin">
            <CheckBox
                android:id="@+id/cb_qualification_item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_292933"
                android:textSize="@dimen/normal_text_size"
                style="@style/CustomCheckboxTheme"
                android:paddingLeft="@dimen/normal_margin"
                android:text=""/>
        </com.ybmmarket20.common.widget.RoundLinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_marginTop="1px"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/normal_margin"
            android:background="@drawable/bg_cart_section_content_03">
            <ImageView
                android:id="@+id/iv_credentials"
                android:layout_width="80dp"
                android:layout_height="90dp"
                android:scaleType="centerInside"/>

            <LinearLayout
                android:layout_marginLeft="@dimen/normal_margin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/tv_credentials_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_292933"
                    android:textSize="@dimen/text_simple_small_size"
                    android:text="证 件 号 码 ："/>
                <TextView
                    android:id="@+id/tv_credentials_indate"
                    android:layout_marginTop="@dimen/normal_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_292933"
                    android:textSize="@dimen/text_simple_small_size"
                    android:text="证件有效期："/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>