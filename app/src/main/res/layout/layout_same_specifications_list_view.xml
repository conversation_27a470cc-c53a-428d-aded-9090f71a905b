<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="10dp"
        android:layout_marginTop="13dp"
        android:textColor="@color/color_292933"
        android:text="同品其他规格"
        android:textSize="15dp"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_content"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginHorizontal="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:overScrollMode="never"
        tools:orientation="horizontal"
        tools:listitem="@layout/item_same_specifacations_list"
        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_height="wrap_content"/>


</androidx.constraintlayout.widget.ConstraintLayout>