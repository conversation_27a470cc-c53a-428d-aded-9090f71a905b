<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_time"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="2dp"
            android:gravity="center_vertical"
            android:text="动态布局标题"
            android:textSize="15sp"/>

        <LinearLayout
            android:id="@+id/ll_time"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="6dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="invisible">

            <TextView
                android:id="@+id/tv_day"
                style="@style/home_seckill_style"
                android:text="12"/>

            <TextView
                android:id="@+id/tv_dot_day"
                style="@style/home_seckill_point"
                android:text=":"/>

            <TextView
                android:id="@+id/tv_hour"
                style="@style/home_seckill_style"
                android:text="12"/>

            <TextView
                style="@style/home_seckill_point"
                android:text=":"/>

            <TextView
                android:id="@+id/tv_minute"
                style="@style/home_seckill_style"
                android:text="50"/>

            <TextView
                style="@style/home_seckill_point"
                android:text=":"/>

            <TextView
                android:id="@+id/tv_second"
                style="@style/home_seckill_style"
                android:text="50"/>
        </LinearLayout>
    </LinearLayout>
    <!--增加自己的固定布局-->

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:paddingLeft="4dp"
        android:scrollbars="none"
        />

</merge>