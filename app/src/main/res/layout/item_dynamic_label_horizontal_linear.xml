<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/con1"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="9dp">

    <LinearLayout
        android:id="@+id/lyLabel"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minWidth="@dimen/pabr_dimen67dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:gravity="center_horizontal"
        android:background="@drawable/filter_btn_bg_selector3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivDynamicLabel"
            android:layout_width="13dp"
            android:layout_height="16dp"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/tvDynamicLabel"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:layout_height="@dimen/pabr_dimen25dp"
            android:gravity="center"
            android:maxEms="5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@drawable/filter_item_btn_textcolor2"
            android:textSize="12sp"
            android:visibility="visible"
            tools:text="仅看中药" />
    </LinearLayout>
    <View
        android:id="@+id/view_bottom"
        android:layout_width="0dp"
        android:layout_height="5dp"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="@id/lyLabel"
        app:layout_constraintStart_toStartOf="@id/lyLabel"
        app:layout_constraintTop_toBottomOf="@id/lyLabel" />
</androidx.constraintlayout.widget.ConstraintLayout>