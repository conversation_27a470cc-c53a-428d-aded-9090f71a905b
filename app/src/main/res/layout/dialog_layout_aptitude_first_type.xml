<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp">
        <com.ybmmarket20.common.widget.RoundRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp">
            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_titles"
                android:layout_width="wrap_content"
                android:layout_height="23dp"
                android:background="@color/white"
                android:gravity="center"
                android:text="请选择类型"
                android:textColor="#FF30303C"
                android:layout_centerInParent="true"
                android:textStyle="bold"
                android:textSize="16sp" />
            <ImageView
                android:id="@+id/iv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/aptitude_exit"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="9dp"
                android:padding="9dp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="9dp" />
        </com.ybmmarket20.common.widget.RoundRelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />
        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_drugstore"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="药店"
            android:textColor="#FF515163"
            android:textSize="16sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_clinic"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="诊所"
            android:textColor="#FF515163"
            android:textSize="16sp" />
        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_new_drugstore_open"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="新药店开办"
            android:textColor="#FF515163"
            android:textSize="16sp" />
        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_preparation"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="药店筹建"
            android:textColor="#FF515163"
            android:textSize="16sp" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>


</LinearLayout>