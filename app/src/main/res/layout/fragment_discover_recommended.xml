<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <com.ybmmarket20.view.X5WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@color/activity_bg"
        android:gravity="center"
        android:lineSpacingExtra="1dp"
        android:lineSpacingMultiplier="1.3"
        android:text="哎呀,网络不给力\n点击重新加载"
        android:textSize="16sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/rl_network_tip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/icon_no_network"
            android:drawablePadding="15dp"
            android:gravity="center"
            android:text="暂无网络"
            android:textColor="#FF9494A6"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_fresh"
            android:layout_width="160dp"
            android:layout_height="44dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/bg_rect_radio_green_shape"
            android:gravity="center"
            android:text="刷新一下"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>

    <!--<?xml version="1.0" encoding="utf-8"?>-->
    <!--<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
    <!--android:orientation="vertical" android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent">-->

    <!--<com.ybm.app.view.CommonRecyclerView-->
    <!--android:id="@+id/rv_recommend"-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent"-->
    <!--android:background="@color/base_bg">-->
    <!--</com.ybm.app.view.CommonRecyclerView>-->
    <!--</LinearLayout>-->
