<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#eeeeee"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/tv_total_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="0.0"
            android:textColor="#00B377"
            android:textSize="30sp"
            android:textStyle="bold"
            tools:text="1000" />

        <TextView
            android:id="@+id/tv_balance_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#9494A6"
            android:textSize="14sp"
            tools:text="累计余额" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#eeeeee" />

    <TextView
        android:id="@+id/tv_balance_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="10dp"
        android:textColor="#292933"
        android:textSize="14sp"
        tools:text="累计余额明细" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#eeeeee" />

    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
