<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">
    <!--滑动菜单布局-->
    <com.ybmmarket20.common.widget.RoundRelativeLayout
        android:id="@+id/bg"
        android:layout_width="90dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/record_red"
        app:rv_cornerRadius="6dp">

        <TextView
            android:layout_width="90dp"
            android:layout_height="80dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:drawableTop="@drawable/ic_delete"
            android:drawablePadding="2dp"
            android:gravity="center"
            android:text="删 除"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </com.ybmmarket20.common.widget.RoundRelativeLayout>
    <!--正常的条目布局-->
    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/fg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingHorizontal="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/white">

        <TextView
            android:id="@+id/tv_shop_title"
            android:layout_width="wrap_content"
            android:maxWidth="400dp"
            android:layout_height="wrap_content"
            android:paddingEnd="4dp"
            android:textColor="@color/color_111111"
            android:textSize="14dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constrainedWidth="true"
            tools:text="湖北小药药自营旗舰店湖北营旗舰店湖北营旗舰店湖北旗舰店湖北旗舰店湖北" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="6dp"
            android:layout_height="10dp"
            android:src="@drawable/icon_order_item_arrow_right"
            app:layout_constraintBottom_toBottomOf="@id/tv_shop_title"
            app:layout_constraintStart_toEndOf="@id/tv_shop_title"
            app:layout_constraintEnd_toStartOf="@id/tv_order_status"
            android:layout_marginEnd="3dp"
            app:layout_constraintTop_toTopOf="@id/tv_shop_title" />

        <TextView
            android:id="@+id/tv_order_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_00b955"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="出库中"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_one"
            android:layout_width="wrap_content"
            app:constraint_referenced_ids="iv_order,tv_goods_num,tv_title,tv_specifications"
            android:layout_height="wrap_content"/>

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/iv_order"
            android:layout_width="78dp"
            android:layout_height="78dp"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toBottomOf="@id/tv_shop_title"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_goods"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_price"
            app:layout_constraintTop_toBottomOf="@id/tv_shop_title"
            android:layout_marginTop="10dp"
            android:layout_width="0dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:layout_height="78dp"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_goods_num"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            app:rv_backgroundColor="@color/color_80000000"
            android:paddingHorizontal="5dp"
            android:minWidth="25dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12dp"
            app:rv_cornerRadius="2dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_order"
            app:layout_constraintEnd_toEndOf="@id/iv_order"
            tools:text="x1" />
        <TextView
            android:id="@+id/tv_create_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#777777"
            android:textSize="14sp"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toBottomOf="@id/iv_order"
            app:layout_constraintStart_toStartOf="@id/iv_order"
            tools:text="下单时间：" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="27dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_color_333333"
            android:textSize="14dp"
            app:layout_constraintEnd_toStartOf="@id/tv_price"
            app:layout_constraintStart_toEndOf="@id/iv_order"
            app:layout_constraintTop_toTopOf="@id/iv_order"
            tools:text="惠海希康 金莲花软胶囊胶囊胶惠海希康 金莲花软胶囊胶囊胶惠海希康 金莲花软胶囊胶囊胶" />

        <TextView
            android:id="@+id/tv_specifications"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textColor="@color/colors_888888"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="规格：规格：6颗*1板*2板" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_111111"
            android:textSize="14dp"
            android:textStyle="bold"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/tv_order_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_order"
            tools:text="294.1" />

        <TextView
            android:id="@+id/tv_order_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="共4种"
            android:textColor="@color/colors_888888"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_order"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_price" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_create_time,rv_goods"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_remark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/barrier"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10">

            <TextView
                android:id="@+id/tv_remark_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:text="商家备注：客户药品经营许可证过期，请尽快更新，更新后可发货客户药品经营许可证过期，请尽快更新，更新后可发货"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="@color/color_292933"
                android:textSize="@dimen/dimen_dp_13"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.ybmmarket20.view.OrderItemAptitudeView
            android:id="@+id/aptitudeView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_remark"
            android:layout_marginTop="10dp"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10" />

        <!--操作按键-->
        <com.ybmmarket20.view.OrderActionLayout
            android:id="@+id/ral_btn"
            android:layout_width="match_parent"
            android:layout_height="51dp"
            app:layout_constraintTop_toBottomOf="@id/aptitudeView" />
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>