<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_gravity="center"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="1px"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="10dp"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart_section_content_03">

        <TextView
            android:id="@+id/cart_head03_tv_subtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="40dp"
            android:layout_toStartOf="@+id/rl_cart_sku"
            android:text="小计:¥180元"
            android:textColor="@color/cart_head_tv02"
            android:textSize="@dimen/cart_content_tv03"
            android:textStyle="bold" />

        <RelativeLayout
            android:id="@+id/rl_cart_sku"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical">

            <include layout="@layout/layout_cart_sku_edit" />

            <TextView
                android:id="@+id/shop_no_tv01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="该商品已下架"
                android:textColor="@color/cart_description"
                android:textSize="@dimen/cart_content_tv02"
                android:visibility="invisible" />

        </RelativeLayout>

    </RelativeLayout>
</LinearLayout>