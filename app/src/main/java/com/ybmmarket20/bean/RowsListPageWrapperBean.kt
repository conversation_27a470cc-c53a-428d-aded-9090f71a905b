package com.ybmmarket20.bean

import com.ybmmarket20.bean.loadmore.IPage

class RowsListPageWrapperBean<T>(
        var skuList: List<T>?,
        var pageSize: Int,
        var pageNum: Int,
        var keyWords: String?,
        var categoryId: String?,
        var totalPage: Int
) : RowsListBean()
        , IPage<T> {
    override fun getCurPage(): Int = pageNum

    override fun getPageRowSize(): Int = pageSize

    override fun getRowsList(): List<T>? = skuList

    override fun getTotalPages(): Int = if (count == 0) count else count / pageSize + 1
}