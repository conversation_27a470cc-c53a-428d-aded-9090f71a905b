package com.ybmmarket20.bean;

public class TVLiveAccountInfo {

    public LiveInfo liveInfo;

    public class LiveInfo{
        /**
         * 房间IM群组ID
         */
        public String roomID;

        /**
         * 房间信息（创建房间时传入）
         */
        public String roomInfo;

        /**
         * 房间名称
         */
        public String roomName;

        /**
         * 房间创建者ID
         */
        public String roomCreator;

        /**
         * 状态见下表
         * 未开始	0
         * 正常直播	1
         * 直播中推流中断	2
         * 已结束推流结束未生成点播	3
         * 已结束推流结束并生成点播	4
         */
        public int liveStatus;

        /**
         * 拉流地址或点播地址
         */
        public String mixedPlayURL;

        /**
         * 房间观众数
         */
        public int audienceCount;

        /**
         * Imuserid
         */
        public String txImUser;
        /**
         * Im Sig
         */
        public String txImSig;

        /**
         * 直播头像
         */
        public String avatar;
    }


}
