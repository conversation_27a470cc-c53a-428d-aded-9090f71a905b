package com.ybmmarket20.bean;

import java.util.List;

/**
 * 购物车顶部title
 */

public class CartActivityBean {

    public String action;//  点击事件
    public int height;
    public String imgUrl;//图片url
    public int width;
    public List<Integer> padding;// [left,top,rigth,bottom] 内边距 单位dp       | int型数组
    public List<Integer> margin;// [left,top,rigth,bottom]  外边距 单位dp       | int型数组
    //2017.9.4
    public int dataType;// 数据类型0，图片数据 1，文本数据
    public String text;//购物车头部显示title
    public String color;//title颜色
    public int fontSize;//title文字大小
    public String backgroundColor;//title背景颜色
    public int align;//购物车文案位置 0.局左，1.居中
    public int cartAvailableVoucherNum;//是否显示优惠券 1显示  其他不显示
    public int isMatch;//  是否满足条件  0 满足 1 不满足

    public boolean isShowVoucher(){
        return cartAvailableVoucherNum==1;
    }

}
