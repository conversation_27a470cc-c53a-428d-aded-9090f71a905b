package com.ybmmarket20.bean

import com.ybm.app.bean.AbstractMutiItemEntity

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/24 15:11
 *    desc   :
 */
data class RemindSubmitBean(
    val msg: String? = ""
)

data class RemindProgressDetailBean(
    val appName: String,
    val code: Int,
    val `data`: RemindProgressDetailData,
    val msg: String,
    val status: String
)

data class RemindProgressDetailData(
    val cancelExpedite: Int,//0,1 是否展示撤销按钮
    val expireTimeRemain: Int = 0,
    val shippingReminderHistory: List<ReminderHistoryList>
)

data class ReminderHistoryList(
    val customFields: CustomFields? = null,
    val eventStatusStr: String? = "",
    val historyCreateTime: String? = ""
): AbstractMutiItemEntity()

data class CustomFields(
    val appealCategory: String? = "",
    val appealDescription: String? = "",
    val appealEvidence: MutableList<String>,
    val prompt: String? = ""
)