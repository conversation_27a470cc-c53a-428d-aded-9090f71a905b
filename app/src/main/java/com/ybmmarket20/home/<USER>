package com.ybmmarket20.home

import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.page.cart.CartReport
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData

abstract class CartFragmentAnalysisV3: BaseFragment() {

    private var isRefreshed = false

    fun trackPv() {
        if (!isRefreshed) {
            isRefreshed = true
        } else CartReport.pvCart(requireActivity())
    }

    /**
     * 点击顶部优惠券
     */
    fun trackClickTopCoupon() {
        CartReport.trackCartComponentTopCouponClick(requireActivity())
    }

    /**
     * 点击底部去凑单
     */
    fun trackClickBottomCoupon() {
        CartReport.trackCartComponentBottomCouponClick(requireActivity())
    }

    /**
     * 点击底部去结算
     */
    fun trackClickToPay(text: String?) {
        CartReport.trackCartComponentToPayClick(requireActivity(), text)
    }


    fun trackBottomTabExposure() {
        SpmUtil.checkAnalysisContext(requireActivity()) {
            (notNullActivity as MainActivity).trackCommonTabComponentExposure(it)
        }
    }


}