//package com.ybmmarket20.home
//
//import android.content.BroadcastReceiver
//import android.content.Context
//import android.content.Intent
//import android.content.IntentFilter
//import android.graphics.Rect
//import androidx.localbroadcastmanager.content.LocalBroadcastManager
//import androidx.recyclerview.widget.GridLayoutManager
//import androidx.recyclerview.widget.RecyclerView
//import android.view.View
//import com.google.gson.reflect.TypeToken
//import com.ybm.app.adapter.YBMBaseAdapter
//import com.ybm.app.bean.NetError
//import com.ybm.app.common.NtpTrustedTime
//import com.ybm.app.view.WrapGridLayoutManager
//import com.ybmmarket20.R
//import com.ybmmarket20.activity.AptitudeActivity
//import com.ybmmarket20.adapter.GoodsListAdapter
//import com.ybmmarket20.bean.BaseBean
//import com.ybmmarket20.bean.HomeConfigBean
//import com.ybmmarket20.bean.RefreshWrapperPagerBean
//import com.ybmmarket20.bean.RowsBean
//import com.ybmmarket20.bean.homesteady.HomeSteady
//import com.ybmmarket20.bean.homesteady.TabItem
//import com.ybmmarket20.common.BaseActivity
//import com.ybmmarket20.common.BaseResponse
//import com.ybmmarket20.common.RequestParams
//import com.ybmmarket20.common.util.ConvertUtils
//import com.ybmmarket20.constant.AppNetConfig
//import com.ybmmarket20.constant.IntentCanst
//import com.ybmmarket20.fragments.*
//import com.ybmmarket20.network.HttpManager
//import com.ybmmarket20.utils.AdapterUtils.getAfterDiscountPrice
//import com.ybmmarket20.utils.AuditStatusHomeFloatManager
//import com.ybmmarket20.utils.AuditStatusSyncUtil
//import com.ybmmarket20.utils.SpUtil
//import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
//import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
//import com.ybmmarket20.view.homesteady.*
//import com.ybmmarketkotlin.activity.FreightAddOnItemActivity
//import com.ybmmarketkotlin.views.homesteady.HomeSteadyShoppingGuideAllView
//import kotlinx.android.synthetic.main.fragment_coupon.crv_refresh_common
//import kotlinx.android.synthetic.main.fragment_home_steady_layout.*
//import java.lang.reflect.Type
//
///**
// * <AUTHOR>
// * @date 2020-04-29
// * @description 首页fragment（静态布局）
// */
//class HomeSteadyLayoutFragment : HomeSteadyAnalysisFragment(), View.OnClickListener {
//
//    override fun onClick(v: View?) {
//        when (v?.id) {
//            R.id.iv_ad_suspension -> getHeavenlyRedEnvelope(iv_ad_suspension)
//        }
//    }
//
//    override fun getType(): Type = object : TypeToken<BaseBean<RefreshWrapperPagerBean<RowsBean>>>() {}.type
//
//    var adapter: GoodsListAdapter? = null
//    var recommendView: HomeSteadyRecommendHotKeyView? = null
//    var fastEntryView: HomeSteadyFastEntryView? = null
//    var shoppingGuideView: HomeSteadyShoppingGuideAllView? = null
//    var firstStreamerView: HomeSteadyStreamerView? = null
//    var secondStreamerView: HomeSteadyStreamerView? = null
//    var topLineView: HomeSteadyTopLineView? = null
//    var bannerView: HomeSteadyBannerView? = null
//    private var gridLayoutManager: WrapGridLayoutManager? = null
//    var totalDy: Int = 0
//    private var br: BroadcastReceiver? = null
//    private var auditStatusHomeFloatManager: AuditStatusHomeFloatManager = AuditStatusHomeFloatManager()
//    private var toTopThreshold = 0f
//    private var isReceiveFlowData = false
//    private var isToTop = true
//
//    override fun getRequestParams(): RequestParams = RequestParams().also {
//        it.put("pageType", "3")//2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐
//        if (isReceiveFlowData) addAnalysisRequestParams(it, mFlowData)
//    }
//
//    override fun getAdapter(rows: MutableList<RowsBean>?): YBMBaseAdapter<RowsBean> {
//        if (adapter == null) {
//            adapter = GoodsListAdapter(R.layout.detail_gridview_item, rows)
//        }
//        return adapter as GoodsListAdapter
//    }
//
//    override fun getUrl(): String = AppNetConfig.HOME_RECOMMENDED_SKU
//
//    override fun getLayoutId(): Int = R.layout.fragment_home_steady_layout
//
//    override fun getStartPage(): Int = 1
//
//    override fun onRefresh() {
//        super.onRefresh()
//        getHeaderData()
//        getHomeAlert(HOME_ALERT_SCENE_REFRESH)
//        getTurnTable(iv_dial_suspension)
//        isSwitchCms()
//    }
//
//    override fun onResume() {
//        super.onResume()
//        getHomeAlert(HOME_ALERT_SCENE_RESUME)
//    }
//
//    override fun initData(content: String?) {
//        val headerView = View.inflate(context, R.layout.header_home_steady, null)
//        gridLayoutManager = context?.let { WrapGridLayoutManager(it, 2) }
//        gridLayoutManager?.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
//            override fun getSpanSize(position: Int): Int {
//                val dataSize = rows.size
//                val spanCount = gridLayoutManager?.spanCount ?: 1
//                return if (position == 0 || position == dataSize + 1) {
//                    spanCount
//                } else {
//                    1
//                }
//            }
//        }
//        getAdapter(rows ?: ArrayList()).addHeaderView(headerView)
//        crv_refresh_common.layoutManager = gridLayoutManager
//        crv_refresh_common.addItemDecoration(HomeSteadyItemDecoration())
//        initView(headerView)
//        initHeaderPlaceHold()
//        initAnalysisCallback()
//        getHeaderData()
//        loadData()
//        getHeavenlyRedEnvelope(iv_ad_suspension)
//        getTurnTable(iv_dial_suspension)
//        getHomeAlert(HOME_ALERT_SCENE_LAUNCH)
//        getMerchantAptitudApproachingDate()
//        initMessageCountForBubble(hssv_search.getBubbleView())
//        handleScroll()
//        NtpTrustedTime.getInstance().check()
//        initBroadCastReceiver()
//        (getAdapter(rows) as GoodsListAdapter).setOnListItemClickListener {
//            openUrl("ybmpage://productdetail?${IntentCanst.PRODUCTID}=${it.id}")
//        }
//        topLineView?.postDelayed({
//            toTopThreshold = topLineView?.top?.toFloat() ?: ConvertUtils.dp2px(600f).toFloat()
//        }, 300)
//    }
//
//    override fun loadData() {
//        if (SpUtil.getMerchantid().isNullOrEmpty()) return
//        var params = requestParams
//        if (params == null) params = RequestParams()
//        if (notNullActivity is FreightAddOnItemActivity) {
//            params.put("pageNum", page.toString() + "")
//            params.put("pageSize", limit.toString() + "")
//        } else {
//            params.put("offset", page.toString() + "")
//            params.put("limit", limit.toString() + "")
//        }
//        params.put("merchantId", SpUtil.getMerchantid())
//        params.put("limit", limit.toString() + "")
//        HttpManager.getInstance().post(url, params, object : BaseResponse<RefreshWrapperPagerBean<RowsBean>>() {
//            override fun onSuccess(content: String, obj: BaseBean<RefreshWrapperPagerBean<RowsBean>>, t: RefreshWrapperPagerBean<RowsBean>) {
//                super.onSuccess(content, obj, t)
//                dismissProgress()
//                crv.setRefreshing(false)
//                if (obj.isSuccess && t != null) {
//                    if (page == startPage) {
//                        rows.clear()
//                    }
//                    val isMoreData = false
//                    if (t.rowsList != null) {
//
//                        rows.addAll(t.rowsList!!)
//                        // 请求并更新折后价
//                        getAfterDiscountPrice(t.rowsList, getAdapter(rows))
//                        onPreSuccess(content, obj, t)
//                    }
//                    getAdapter(rows).notifyDataChangedAfterLoadMore(t.rowsList != null && t.rowsList!!.size >= size)
//                }
//                onResponseSuccess(content, obj, t)
//            }
//
//            override fun onFailure(error: NetError) {
//                super.onFailure(error)
//                dismissProgress()
//                crv.setRefreshing(false)
//                if (rows == null) rows = arrayListOf()
//                getAdapter(rows).setNewData(rows)
//                if (page != startPage) page--
//                onResponseFailure(error)
//            }
//        })
//    }
//
//    /**
//     * 初始化View
//     */
//    private fun initView(headerView: View) {
//        recommendView = headerView.findViewById(R.id.hsrv_recommend)
//        fastEntryView = headerView.findViewById(R.id.hsfev_fast_entry)
//        shoppingGuideView = headerView.findViewById(R.id.hssv_shopping_guide)
//        firstStreamerView = headerView.findViewById(R.id.hssv_first_streamer)
//        secondStreamerView = headerView.findViewById(R.id.hssv_second_streamer)
//        topLineView = headerView.findViewById(R.id.hstlv_top_line)
//        bannerView = headerView.findViewById(R.id.home_banner)
//    }
//
//    /**
//     * 初始化头部占位图
//     */
//    private fun initHeaderPlaceHold() {
//        recommendView?.initPlaceHold()
//        fastEntryView?.initPlaceHold()
//        shoppingGuideView?.initPlaceHold()
//        firstStreamerView?.initPlaceHold()
//        secondStreamerView?.initPlaceHold()
//        secondStreamerView?.displayGradient(true)
//        bannerView?.initPlaceHold()
//    }
//
//    /**
//     * 初始化埋点回调
//     */
//    private fun initAnalysisCallback() {
//        bannerView?.setBannerAnalysisCallback(::bannerAnalysisCallback)
////        fastEntryView?.setAnalysisCallback(::fastEntryAnalysisCallback)
//        firstStreamerView?.setAnalysisCallback(::streamerAnalysisCallback)
//        secondStreamerView?.setAnalysisCallback(::streamerAnalysisCallback)
//        shoppingGuideView?.setAnalysisCallback(::shoppingGuideAnalysisCallback)
//    }
//
//    /**
//     * 获取头部数据
//     */
//    override fun getHeaderData() {
//        HttpManager.getInstance().post(AppNetConfig.HOME_INDEX_DATA, RequestParams(), object : BaseResponse<HomeSteady>() {
//            override fun onSuccess(content: String?, obj: BaseBean<HomeSteady>?, t: HomeSteady?) {
//                super.onSuccess(content, obj, t)
//                t?.also {
//                    // t.modules?.shoppingGuide?.content?.shoppingGuideAllList?.get(0)?.itemType = HOME_STEADY_LAYOUT_TIMER
//                    recommendView?.setRecommendSearchKeyData(t.modules?.searchBox?.content?.hostSearchList)
//                    bannerView?.setData(t.modules?.searchBox?.content?.bannerList)
//                    fastEntryView?.setFastEntryData(t.modules?.fastEntry?.fastEntryContent?.fastEntryItemList)
//                    if(t.modules == null || t.modules!!.headline == null || t.modules!!.headline!!.content == null
//                        || t.modules!!.headline!!.content!!.headLineList == null || t.modules!!.headline!!.content!!.headLineList!!.isEmpty()) {
//                        topLineView?.visibility = View.GONE
//                    } else {
//                        topLineView?.setData(t.modules?.headline?.content?.headLineList)
//                        topLineView?.visibility = View.VISIBLE
//                    }
//                    shoppingGuideView?.setshoppingGuideData(t.modules?.newShoppingGuide?.content?.shoppingGuideAllList)
//                    shoppingGuideView?.setLicenseStatus(t.licenseStatus)
//                    if (t.modules?.firstStreamer == null) {
//                        firstStreamerView?.visibility = View.GONE
//                    } else {
//                        firstStreamerView?.setStreamer(t.modules?.firstStreamer?.content?.streamerItemList?.get(0))
//                        firstStreamerView?.visibility = View.VISIBLE
//                    }
//                    if (t.modules?.secondStreamer == null) {
//                        secondStreamerView?.visibility = View.GONE
//                    } else {
//                        secondStreamerView?.setStreamer(t.modules?.secondStreamer?.content?.streamerItemList?.get(0))
//                        secondStreamerView?.visibility = View.VISIBLE
//                    }
//                    setTabs(t.modules?.tabbar?.tabContent?.tabList)
//                    updateLicenseStatus(t.licenseStatus, getCurrentLicenseStatusListener())
//                    handleAuditPassedFloatView(!AuditStatusSyncUtil.getInstance().isAuditFirstPassed)
//                }
//                topLineView?.postDelayed({
//                    toTopThreshold = topLineView?.top?.toFloat()
//                            ?: ConvertUtils.dp2px(600f).toFloat()
//                }, 300)
//            }
//
//            override fun onFailure(error: NetError?) {
//                super.onFailure(error)
//            }
//        })
//    }
//
//    /**
//     * 设置首页tab
//     */
//    private fun setTabs(tabList: MutableList<TabItem>?) {
//        val homeConfigBean = HomeConfigBean()
//        tabList?.forEachIndexed { index, tabItem ->
//            when (index) {
//                0 -> {
//                    homeConfigBean.bottom_first_button_img_url = tabItem.image
//                    homeConfigBean.bottom_first_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_first_button_text = tabItem.text
//                }
//                1 -> {
//                    homeConfigBean.bottom_second_button_img_url = tabItem.image
//                    homeConfigBean.bottom_second_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_second_button_text = tabItem.text
//                }
//                2 -> {
//                    homeConfigBean.bottom_fifth_button_img_url = tabItem.image
//                    homeConfigBean.bottom_fifth_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_fifth_button_text = tabItem.text
//                }
//                3 -> {
//                    homeConfigBean.bottom_third_button_img_url = tabItem.image
//                    homeConfigBean.bottom_third_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_third_button_text = tabItem.text
//                }
//                4 -> {
//                    homeConfigBean.bottom_fourth_button_img_url = tabItem.image
//                    homeConfigBean.bottom_fourth_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_fourth_button_text = tabItem.text
//                }
//            }
//        }
//        (notNullActivity as MainActivity).setActivity(homeConfigBean)
//    }
//
//    /**
//     * 回到顶部
//     */
//    override fun toTop() {
//        crv_refresh_common.recyclerView.scrollToPosition(0)
//        (crv_refresh_common.recyclerView.layoutManager as GridLayoutManager).scrollToPositionWithOffset(0, 0)
//        crv_refresh_common.postDelayed({
//            totalDy = 0
//            (notNullActivity as MainActivity).handleToTopBtn(false)
//        }, 200)
//    }
//
//    /**
//     * 监听滚动距离控制首页tab回到底部按钮
//     */
//    private fun handleScroll() {
//        crv_refresh_common.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//                totalDy -= dy
//                var temp = totalDy < -toTopThreshold
//                if (isToTop != temp) {
//                    isToTop = temp
//                    (notNullActivity as MainActivity).handleToTopBtn(isToTop)
//                }
//
//            }
//        })
//    }
//
//    /**
//     * 初始化广播
//     */
//    private fun initBroadCastReceiver() {
//        br = object : BroadcastReceiver() {
//            override fun onReceive(context: Context, intent: Intent) {
//                if (IntentCanst.REFRESH_PAGE == intent.action) {
//                    getHeaderData()
//                } else if (IntentCanst.ACTION_SWITCH_USER == intent.action) {
//                    isReceiveFlowData = false
//                    toTop()
//                    resetPage()
//                    loadData()
//                    isSwitchCms()
//                    getTurnTable(iv_dial_suspension)
//                } else if (IntentCanst.ACTION_AD_COLLECT_POP == intent.action) run {
//                    iv_ad_suspension.visibility = View.VISIBLE
//                } else if (IntentCanst.ACTION_AD_COLLECT_HINT_POP == intent.action) run {
//                    iv_ad_suspension.visibility = View.GONE
//                } else if (IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE == intent.action) {
//                    initMessageCountForBubble(hssv_search.getBubbleView())
//                } else if (IntentCanst.ACTION_LOGOUT == intent.action) {
//                    // 退出登录时候
//                }
//            }
//        }
//        val intentFilter = IntentFilter(IntentCanst.MSG_NUM_EDIT)
//        intentFilter.addAction(IntentCanst.REFRESH_PAGE)
//        intentFilter.addAction(IntentCanst.ACTION_LOGOUT)
//        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER)
//        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_POP)
//        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_HINT_POP)
//        intentFilter.addAction(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE)
//        LocalBroadcastManager.getInstance(notNullActivity).registerReceiver(br as BroadcastReceiver, intentFilter)
//    }
//
//    // 获取是否开启cms
//    private fun isSwitchCms() {
//        val params = RequestParams.newBuilder().url(AppNetConfig.GET_LAYOUT_TYPE).build()
//        // old使用原admin布局，new使用cms配置布局
//        HttpManager.getInstance().post(params, object : BaseResponse<String>() {
//            override fun onSuccess(content: String, obj: BaseBean<String>, s: String) {
//                if (!"newLayout".equals(s, ignoreCase = true)) {
//                    //切换到cms
//                    context?.let { LocalBroadcastManager.getInstance(it).sendBroadcast(Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE)) }
//                }
//            }
//        })
//
//    }
//
//    override fun onDestroyView() {
//        super.onDestroyView()
//        if (br != null) {
//            LocalBroadcastManager.getInstance(notNullActivity).unregisterReceiver(br!!)
//        }
//    }
//
//    override fun onLicenseStatusEnable(): Boolean = true
//
//    override fun handleLicenseStatusChange(status: Int) {
//        super.handleLicenseStatusChange(status)
////        getHeaderData()
////        resetPage()
////        loadData()
//        getAdapter(rows).notifyDataSetChanged()
//        handleAuditPassedFloatView(!AuditStatusSyncUtil.getInstance().isAuditFirstPassed)
//        shoppingGuideView?.setLicenseStatus(status)
//    }
//
//    override fun onResponseSuccess(content: String?, obj: BaseBean<RefreshWrapperPagerBean<RowsBean>>?, t: RefreshWrapperPagerBean<RowsBean>?) {
//        super.onResponseSuccess(content, obj, t)
//        t?.licenseStatus?.let { updateLicenseStatus(it, getCurrentLicenseStatusListener()) }
//        if (!isReceiveFlowData) {
//            t?.also {
//                mFlowData.spId = it.spId
//                mFlowData.spType = it.spType
//                mFlowData.sId = it.sid
//                isReceiveFlowData = true
//                flowDataPageCommoditySearch(mFlowData)
//                (getAdapter(rows) as GoodsListAdapter).setFlowData(mFlowData)
//            }
//        }
//    }
//
//    /**
//     * 处理一审悬浮弹窗是否显示
//     * @param isShow 是否显示
//     */
//    private fun handleAuditPassedFloatView(isShow: Boolean) {
////        val vg = loadingPage.findViewById<ViewGroup>(R.id.cl_home_steady)
//        val vg = ll_mask
//        if (isShow) {
//            context?.let {
//                auditStatusHomeFloatManager.addParentLayout(it, vg)
//                        .setOnAuditStatusFloatVieListener(object : AuditStatusHomeFloatManager.AuditStatusFloatViewListener {
//                            override fun callback() {
//                                (notNullActivity as BaseActivity).gotoAtivity(AptitudeActivity::class.java, null)
//                            }
//                        })
//            }
//        } else {
//            auditStatusHomeFloatManager.hiddenFloatView()
//        }
//    }
//}
//
///**
// * 列表网格分割线
// */
//class HomeSteadyItemDecoration : RecyclerView.ItemDecoration() {
//    private val space = ConvertUtils.dp2px(1f)
//
//    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
//        if (parent.getChildLayoutPosition(view) == 0) {
//            outRect.top = 0
//            outRect.bottom = 0
//            outRect.left = 0
//            outRect.right = 0
//        } else {
//            outRect.bottom = 0
//            if (parent.getChildLayoutPosition(view) % 2 == 1) {
//                outRect.left = space * 8
//                outRect.right = space * 4
//            } else {
//                outRect.left = space * 4
//                outRect.right = space * 8
//            }
//
//            if (parent.getChildLayoutPosition(view) > 0) {
//                outRect.top = space * 8
//            } else {
//                outRect.top = 0
//            }
//        }
//    }
//}