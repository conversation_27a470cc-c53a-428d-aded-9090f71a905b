package com.ybmmarket20.utils;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.xyyio.analysis.util.DeviceInfoUtils;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.bean.LoginInfo;
import com.ybmmarket20.bean.LoginMerchantInfo;
import com.ybmmarket20.bean.MerchantInfo;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.constant.SpConstantKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.xyyreport.XyyReportManager;
import com.ybmmarketkotlin.utils.ChCrypto;
import com.ydmmarket.report.manager.TrackManager;

public class SpUtil extends com.ybm.app.utils.SpUtil {

    public static String LOGIN_INFO = "login_info";
    public static String MERCHANT_INFO = "merchant_info";
    public static String MERCHANT_BASE_INFO = "merchant_base_info";

    public static void setLoginInfo(LoginInfo loginInfo){
        String accountId = "";
        if (loginInfo != null){
            Gson gson = new Gson();
            String loginInfoStr = gson.toJson(loginInfo);
            writeString(LOGIN_INFO, loginInfoStr);
            if (loginInfo.getAccountId() != null){
                accountId = loginInfo.getAccountId();
            }
        }else {
            writeString(LOGIN_INFO, "");
        }

        JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_ACCOUNT_ID,accountId);
    }

    public static LoginInfo getLoginInfo(){
        String loginInfoStr = readString(LOGIN_INFO, "");
        if (TextUtils.isEmpty(loginInfoStr)) {
            return null;
        }else {
            try {
                Gson gson = new Gson();
                LoginInfo loginInfo = gson.fromJson(loginInfoStr, LoginInfo.class);
                return loginInfo;
            }catch (Exception e){
                e.printStackTrace();
                return null;
            }
        }
    }

    public static void setMerchantInfo(LoginMerchantInfo merchantInfo){
        String companyName = "";
        if (merchantInfo != null){
            Gson gson = new Gson();
            String merchantInfoStr = gson.toJson(merchantInfo);
            writeString(MERCHANT_INFO, merchantInfoStr);
            if (merchantInfo.getShopName() != null){
                companyName = merchantInfo.getShopName();
            }
        }else {
            writeString(MERCHANT_INFO, "");
        }
        JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_MERCHANT_NAME,companyName);
    }

    public static void setMerchantBaseInfo(MerchantInfo.BaseInfo baseInfo){
        if (baseInfo != null){
            Gson gson = new Gson();
            String merchantInfoStr = gson.toJson(baseInfo);
            writeString(MERCHANT_BASE_INFO, merchantInfoStr);
            String province ="";
            String businessType = "";
            String businessTypeName = "";
            if (baseInfo.businessType!= null) {
                businessType = baseInfo.businessType;
            }
            if (baseInfo.businessTypeName != null){
                businessTypeName = baseInfo.businessTypeName;
            }
            if (baseInfo.province !=null){
                province = baseInfo.province;
            }

            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_PROVINCE,province);
            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_BUSINESSTYPE,businessType);
            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_BUSINESSTYPENAME,businessTypeName);
        }else {
            writeString(MERCHANT_BASE_INFO, "");
            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_PROVINCE,"");
            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_BUSINESSTYPE,"");
            JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_BUSINESSTYPENAME,"");
        }
    }

    public static MerchantInfo.BaseInfo getMerchantBaseInfo(){
        String merchantInfoStr = readString(MERCHANT_BASE_INFO, "");
        if (TextUtils.isEmpty(merchantInfoStr)) {
            return null;
        }else {
            try {
                Gson gson = new Gson();
                MerchantInfo.BaseInfo merchantInfo = gson.fromJson(merchantInfoStr, MerchantInfo.BaseInfo.class);
                return merchantInfo;
            }catch (Exception e){
                e.printStackTrace();
                return null;
            }
        }
    }

    public static LoginMerchantInfo getMerchantInfo(){
        String merchantInfoStr = readString(MERCHANT_INFO, "");
        if (TextUtils.isEmpty(merchantInfoStr)) {
            return null;
        }else {
            try {
                Gson gson = new Gson();
                LoginMerchantInfo merchantInfo = gson.fromJson(merchantInfoStr, LoginMerchantInfo.class);
                return merchantInfo;
            }catch (Exception e){
                e.printStackTrace();
                return null;
            }
        }
    }



    public static String getMerchantid() {
        return readString("MERCHANTID", "");
    }

    public static String getToken() {
        return readString("token", "");
    }

    public static String getAccountId() {
        return readString("accountId", "");
    }

    public static String getDeviceId() {
        String deviceId = readString("new_deviceId", "");
        if (TextUtils.isEmpty(deviceId)) {
            deviceId = DeviceInfoUtils.getDeviceId(BaseYBMApp.getAppContext());
        } else {
            return deviceId;
        }
//        if (TextUtils.isEmpty(deviceId)) {
//            BugUtil.sendBug(new NullPointerException("生成uuid异常了"));
//            DeviceEntity deviceEntity = BaseYBMApp.getApp().getDeviceEntity();
//            if (deviceEntity.code == null || deviceEntity.code.isEmpty()) {
//                deviceEntity.code = deviceEntity.getDeviceId(BaseYBMApp.getAppContext());
//                SpUtil.writeString(BaseYBMApp.KEY_DEVICEENTITY, JsonUtils.toJson(deviceEntity));
//            }
//            deviceId = deviceEntity.code;
//        }
        setDeviceId(deviceId);
        return deviceId;
    }

    public static void setToken(String token) {
        writeString("token", token);
    }

    public static void setAccountId(String accountId) {
        writeString("accountId", accountId);
    }

    public static void setLoginPhone(String phone) {
//        String formatPhone = new String(Base64.encode(phone.getBytes(StandardCharsets.UTF_8), Base64.DEFAULT), StandardCharsets.UTF_8);
        String formatPhone = ChCrypto.INSTANCE.base64Encode(phone);
        writeString("PHONE_NEW", formatPhone);
    }

    public static void setPw(String pw) {
        writeString("pw", pw);
    }

    public static String getPw() {
        return readString("pw", "");
    }

    public static String getLoginPhone() {
        String originalPhone = readString("PHONE_NEW", "");
        try {
//            return new String(Base64.decode(originalPhone.getBytes(StandardCharsets.UTF_8), Base64.DEFAULT), StandardCharsets.UTF_8);
            return ChCrypto.INSTANCE.base64Decode(originalPhone);
        } catch (Exception e) {
            return originalPhone;
        }
    }

    public static String getLoginPhoneOld() {
        return readString("LOGIN_PHONE", "");
    }

    public static void setMerchantid(String merchantId) {
        String companyId = "";
        if (merchantId == null || merchantId.isEmpty()){
            setMerchantInfo(null);
        }else {
            companyId = merchantId;
        }

        writeString("MERCHANTID", merchantId);
        JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),JGTrackManager.Common.FIELD_MERCHANT_ID,companyId);
        TrackManager.alias(YBMAppLike.getAppContext(), merchantId);
    }

    public static void setDeviceId(String deviceId) {
        writeString("new_deviceId", deviceId);
    }

    /**
     * 是否是ka用户,此处为虚拟数据，应该删除ka相关逻辑
     *
     * @return true 是
     */
    public static boolean isKa() {
        return false;
    }

    /**
     * 是否是ka用户
     *
     * @return true 是
     */
    public static boolean isKaTrue() {
        return readBoolean(SpConstantKt.SP_KEY_LOGIN_IS_KA, false);
    }

    public static void clearMerchantid() {
        //退出登录用户id设置为0
        XyyIoUtil.identify("0");
        XyyReportManager.signOff();
        remove("MERCHANTID");
        remove("token");
        remove("STATUS");
        remove("LOGIN_PHONE");
        remove("PHONE_NEW");
    }

    /**
     * pushToken保存 registerPushtoken接口注册后返回的pushToken 需要传给pushTokenBindMember接口绑定
     */
    public static void setPushToken(String pushToken) {
        writeString("pushToken", pushToken);
    }

    public static String getPushToken() {
        return readString("pushToken", "");
    }

    /**
     * 0：正常，1：过期，2：临期
     *
     * @param validity
     */
    public static void setValidityStatus(int validity) {
        writeInt("validity", validity);
    }

    /**
     * 0：正常，1：过期，2：临期
     */
    public static int getValidityStatus() {
        return readInt("validity", 0);
    }

    /**
     * 设置平安蒙层是否提示过
     * @param isTip true: 提示过 false: 未提示过
     */
    public static void setPingAnIsTip(boolean isTip) {
        writeBoolean("isTip", isTip);
    }

    /**
     * 获取平安蒙层是否提示过
     * @return
     */
    public static boolean getPingAnIsTip() {
        return readBoolean("isTip", false);
    }


    /**
     * 设置平安蒙层是否提示过V2
     * @param isTip true: 提示过 false: 未提示过
     */
    public static void setPingAnIsTipV2(boolean isTip) {
        writeBoolean("isTipV2", isTip);
    }

    /**
     * 获取平安蒙层是否提示过V2
     * @return
     */
    public static boolean getPingAnIsTipV2() {
        return readBoolean("isTipV2", false);
    }


    /**
     * 设置平安蒙层是否提示过
     * @param isTip true: 提示过 false: 未提示过
     */
    public static void setUnPingAnIsTip(boolean isTip) {
        writeBoolean("isUnPingAnTip", isTip);
    }

    /**
     * 获取平安蒙层是否提示过V2
     * @return
     */
    public static boolean getUnPingAnIsTip() {
        return readBoolean("isUnPingAnTip", false);
    }

    /**
     * 获取支付指纹状态
     * @return
     */
    public static boolean getPayFingerprintStatus() {
        return readBoolean("payFingerprintStatus", false);
    }

    /**
     * 设置支付指纹状态
     */
    public static void setPayFingerprintStatus(boolean fingerprintStatus) {
        writeBoolean("payFingerprintStatus", fingerprintStatus);
    }

    /**
     * 获取资质提醒
     * @return
     */
    public static String getAptitudeTip() {
        return readString("aptitudeTip", null);
    }

    /**
     * 写入资质提醒
     * @param tip
     */
    public static void setAptitudeTip(String tip) {
        writeString("aptitudeTip", tip);
    }

    /**
     * 获取申请售后引导
     * @return
     */
    public static boolean getApplyAfterSaleGuide() {
        return readBoolean("applyAfterSalesGuide", false);
    }

    /**
     * 设置申请售后引导
     *
     * @return
     */
    public static void setApplyAfterSaleGuide(boolean applyAfterSalesGuide) {
        writeBoolean("applyAfterSalesGuide", applyAfterSalesGuide);
    }
}
