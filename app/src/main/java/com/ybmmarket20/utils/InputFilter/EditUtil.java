package com.ybmmarket20.utils.InputFilter;

import android.os.Build;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.EditText;


import com.ybmmarket20.common.util.ToastUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EditUtil {
    public static final String CONTACT_TAG_PATTERN = "[\\u4e00-\\u9fa5a-zA-Z0-9，,。.：:（）()!！]{1,8}";
    public static final String ILLEGAL_PATTERN = "[`·’~!@#$%^&*+=|{}:;,\\[\\].<>/?~！@#¥¥%……&*——+|{}【】‘；：”“’。，、？]";
    public static final String DEFAULT_PATTERN = "^[\\u4e00-\\u9fa5，,。.：:（）()!！a-zA-Z0-9]+$";
    public static final String DEFAULT_PASSWORD_INPUT = "^[a-zA-Z0-9~!@#¥¥%&*]+$";
    public static final String DEFAULT_PASSWORD_NEW = "^(?=.*?[0-9])(?=.*?[a-zA-Z])[0-9A-Za-z~!@#¥¥%&*]{6,16}$";
    public static final String DEFAULT_PATTERN_NAME = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$";

    public static class InputBuilder {
        List<InputFilter> filters = new ArrayList<>();

        public InputBuilder Builder() {
            return new InputBuilder();
        }

        public InputBuilder setInputLength(int length) throws Exception {
            if (length <= 0) {
                throw new Exception("length must bigger than zero");
            }
            InputFilter.LengthFilter lengthFilter = new InputFilter.LengthFilter(length);
            filters.add(lengthFilter);
            return this;
        }

        public InputBuilder addIllegalInput() {//仅在输入的时候，‘这个符号不能在输入的时候屏蔽，需要在提交时再次判断
            InputFilter filter = new InputFilter() {
                @Override
                public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                    String speChat = "[`~!@#$%^&*+-=|{}:;,\\[\\].<>/?~！@#¥¥%……&*——+|{}【】‘；：”“’。，、？ ]";
                    Pattern pattern = Pattern.compile(speChat);
                    Matcher matcher = pattern.matcher(source.toString());
                    if (matcher.find()) {
                        return "";
                    } else {
                        return null;
                    }
                }
            };
            filters.add(filter);
            return this;
        }

        public InputBuilder matchPattern(final String p) {
            InputFilter filter = new InputFilter() {
                @Override
                public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                    Pattern pattern = Pattern.compile(p);
                    Matcher matcher = pattern.matcher(source.toString());
                    if (matcher.matches()) {
                        return null;
                    } else {
                        return "";
                    }
                }
            };
            filters.add(filter);
            return this;
        }

        public InputBuilder findPattern(final String p) {
            InputFilter filter = new InputFilter() {
                @Override
                public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                    Pattern pattern = Pattern.compile(p);
                    Matcher matcher = pattern.matcher(source.toString());
                    if (matcher.find()) {
                        return "";
                    } else {
                        return null;
                    }
                }
            };
            filters.add(filter);
            return this;
        }

        public InputFilter[] build() {
            return filters.toArray(new InputFilter[filters.size()]);
        }
    }

    public static void getLength(EditText et, int length) {

    }

    //禁止输入空格
    public static void setEditTextInhibitInputpace(EditText et_names) {
        InputFilter filter = new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned
                    dest, int dstart, int dend) {
                if (source.equals(" ")) {
                    return "";
                } else {
                    return null;
                }
            }
        };
        et_names.setFilters(new InputFilter[]{filter});
    }

    //禁止添加非法字符
    public static void setEditTextInhibitInputIllegaCharacter(EditText et_name, int maxLength) {
        et_name.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength), new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source) || Pattern.matches(DEFAULT_PATTERN, source.toString())) {
                    return source;
                } else {
                    return "";
                }
            }
        }});
    }

    //禁止添加非法字符
    public static void setEditTextInhibitInputIllegaCharacter(EditText et_name, int maxLength, final String pattern) {
        et_name.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength), new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source) || Pattern.matches(pattern, source.toString())) {
                    return source;
                } else {
                    return "";
                }
            }
        }});
    }

    public static void setEditTextInhibitInputIllegaCharacter(EditText et_name) {
        et_name.setFilters(new InputFilter[]{new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source) || Pattern.matches(DEFAULT_PATTERN, source.toString())) {
                    return source;
                } else {
                    return "";
                }
            }
        }});
    }

    public static void setEditTextInhibitInputIllegaCharacter(EditText et_name, final String filter) {
        et_name.setFilters(new InputFilter[]{new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source) || Pattern.matches(filter, source.toString())) {
                    return source;
                } else {
                    return "";
                }
            }
        }});
    }

    public static void setPasswordFilter(EditText et_name, final String filter, int maxLength) {
        et_name.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength), new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (TextUtils.isEmpty(source) || Pattern.matches(filter, source.toString())) {
                    return source;
                } else {
                    if (et_name.getText().length() < 16) {
                        ToastUtils.showLong("请不要输入非法字符，请重新输入");
                    }
                    return "";
                }
            }
        }});
    }

    public static boolean checkPhone(String phone) {
        String speChat = "^1\\d{10}$";
        String fixedReg = "^(0[0-9]{2})\\d{8}$|^(0[0-9]{3}(\\d{7,8}))$";
        Pattern pattern = Pattern.compile(speChat);
        Matcher matcher = pattern.matcher(phone);
        if (!phone.matches(fixedReg) && !phone.matches(speChat)) {
            ToastUtils.showShort("请输入正确的手机号码");
            return false;
        } else {
            return true;
        }
    }

    public static boolean checkPasswor(String password) {
        Pattern pattern = Pattern.compile(DEFAULT_PASSWORD_NEW);
        Matcher matcher = pattern.matcher(password);
        return matcher.matches();
    }

    /**
     * 设置 editText 不支持选择复制粘贴
     *
     * @param editText
     */
    public static void setEditNotPaste(EditText editText) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            editText.setLongClickable(false);
            editText.setCustomSelectionActionModeCallback(new ActionMode.Callback() {
                @Override
                public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                    return false;
                }

                @Override
                public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                    return false;
                }

                @Override
                public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                    return false;
                }

                @Override
                public void onDestroyActionMode(ActionMode mode) {

                }
            });
            editText.setCustomInsertionActionModeCallback(new ActionMode.Callback() {
                @Override
                public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                    return false;
                }

                @Override
                public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                    return false;
                }

                @Override
                public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                    return false;
                }

                @Override
                public void onDestroyActionMode(ActionMode mode) {

                }
            });
        }
    }
}
