package com.ybmmarket20.common;


import android.app.Application;
import android.app.Dialog;
import android.content.Context;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

import com.iflytek.cloud.Setting;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechUtility;
import com.pingan.bank.kyb_sdk.KybSdk;
import com.pingan.extend.KybInitListener;
import com.secneo.sdk.Helper;
import com.tencent.mmkv.MMKV;
import com.xyy.canary.AppUpdate;
import com.xyy.canary.utils.AppUtil;
import com.xyy.push.XyyPushClientManager;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.Utils;
import com.ybm100.app.push.PushManager;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.TbsUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.YbmPushUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.waf.XyyWafManager;
import com.ybmmarket20.xyyreport.XyyReportManager;
import com.ydmmarket.report.ReportManager;

import ly.count.android.sdk.XyyApmCly;

/**
 * 隐私相关的启动项管理
 */
public enum PrivacyInitManager {

    INSTANCE;


    //    public static final String SPEECH_KEY = "=584fbf9b";
    public static final String SPEECH_KEY = "=583bd132";


    /**
     * 是否同意过隐私协议
     */
    public boolean isAgreedPrivacy() {
        return SpUtil.readInt(IntentCanst.MENTION, 0) != 0;
    }

    public boolean isMention() {
        int i = SpUtil.readInt(IntentCanst.MENTION, 0);
        return i == 1;
    }

    public void showPrivacyAgreementDialog(PrivacyDialog.OnClickListener listener,ClickableSpanCallback clickableSpanCallback) {
        int i = SpUtil.readInt(IntentCanst.MENTION, 0);
        if (i == 0) {
            PrivacyDialog privacyDialog = new PrivacyDialog(YBMAppLike.getApp().getCurrActivity(), new PrivacyDialog.OnClickListener() {
                @Override
                public void onPositiveClicked(Dialog alertDialog) {
                    alertDialog.dismiss();
                    SpUtil.writeInt(IntentCanst.MENTION, 1);
                    handleInitInternal();
                    listener.onPositiveClicked(alertDialog);
                }

                @Override
                public void onNegativeClicked(Dialog alertDialog) {
                    alertDialog.dismiss();
                    listener.onNegativeClicked(alertDialog);
                }
            });
            privacyDialog.setTitle("用户隐私保护提示");
            privacyDialog.setContent(getClickableSpan(clickableSpanCallback));
            privacyDialog.show();
        } else {
            listener.onPositiveClicked(null);
        }
    }

    private void handleInitInternal() {
        Application appContext = YBMAppLike.getAppContext();
        initJgAnalysys(YBMAppLike.getApp());
        initXyyIO(YBMAppLike.getApp());
        initXyyPush();
        initTbs(appContext);
        initBugly();
        initPushManager(appContext);
//        initVoice();
        initPushToken(appContext);
        initApmCly();
        initUpdate(YBMAppLike.getApp());
        installPingAn(appContext);
        initPingAn(appContext);
        initWaf(appContext);
        initXyyReport(appContext);
    }

    /**
     * 未同意隐私协议前，不能使用ActivityManager方法来判断进程
     */
    private boolean isMainProcess(Context context) {
        if (isAgreedPrivacy()) {
            return context.getPackageName().equals(Utils.getCurrentProcessName(context));
        } else {
            return context.getPackageName().equals(Utils.getCurrentProcessNameNoPrivacy());
        }
    }

    // 雪地sdk
    public void initXyyIO(Application context) {
        if (!isAgreedPrivacy()) {
            return;
        }

        if (!isMainProcess(context)) {
            return;
        }

        XyyIoUtil.init(context);
    }

    // 腾讯x5浏览器
    public void initTbs(Context context) {
        if (!isAgreedPrivacy()) {
            return;
        }
        TbsUtil.Companion.getInstance(context).beforeInit();
        TbsUtil.Companion.getInstance(context).downloadListener();
        TbsUtil.Companion.getInstance(context).initX5();
    }

    // bugly
    public void initBugly() {
        if (!isAgreedPrivacy()) {
            return;
        }
        if (Looper.getMainLooper() == Looper.myLooper()) {
            // 主线程
            new Thread(new Runnable() {
                @Override
                public void run() {
                    initBuglyInternal();
                }
            }).start();
        } else {
            initBuglyInternal();
        }
    }

    private void initBuglyInternal() {
        BugUtil.initBug(BuildConfig.bugly_app_id, BuildConfig.bugly_app_key);
    }

    public void initXyyPush() {
        if (!isAgreedPrivacy()) {
            return;
        }
        XyyPushClientManager.getInstance().setDebug(BuildConfig.DEBUG);
        XyyPushClientManager.getInstance().init(BaseYBMApp.getAppContext());

    }

    public void initMMKV(Context context) {
        String rootDir = MMKV.initialize(context);
        System.out.println("mmkv root: " + rootDir);
    }

    // 旧版push（极光）
    public void initPushManager(Context context) {
        if (!isAgreedPrivacy()) {
            PushManager.setAuth(context, false);
            return;
        }
        PushManager.setAuth(context, true);
        if (Looper.getMainLooper() == Looper.myLooper()) {
            // 主线程
            new Thread(new Runnable() {
                @Override
                public void run() {
                    initPushManagerInternal(context);
                }
            }).start();
        } else {
            initPushManagerInternal(context);
        }
    }

    public void initApmCly() {
        if (!isAgreedPrivacy()) {
            return;
        }

        // 测试环境下：ybm_test（appkey）
        final String APP_TEST_KEY = "4d9f1a953fd03aa75b39faae81651427c86a0be1";
        // 正式环境下：ybm（appkey）
        final String APP_KEY = "ae93b99720f77a028dbb14beec3243bc7b14c39d";

        String ybmUser = "unlogin";
        String ybmUserId = SpUtil.getMerchantid();
        if (!TextUtils.isEmpty(ybmUserId)) {
            ybmUser = SpUtil.getLoginPhone();
        } else {
            ybmUserId = "unlogin";
        }

        XyyApmCly.getInstance()
                .setApplication(YBMAppLike.getAppContext())
                .setAppKey(BuildConfig.DEBUG ? APP_TEST_KEY : APP_KEY)
                .setDeviceId(SpUtil.getDeviceId())
                .setUser(ybmUser, ybmUserId)
                .initialize(!BuildConfig.DEBUG, BuildConfig.DEBUG ? 1 : 20);
    }

    public void initUpdate(Application application) {
        if (!isAgreedPrivacy()) {
            return;
        }
        //埋点
        AppUpdate.getInstance()
                .init(application)
                // 依据当前Build.gradle 文件判断是否走线上或线下环境
                .isProdEnviroment(!BuildConfig.DEBUG)
                // 线上或者线下app 在canary系统中的id   https://canary-admin.test.ybm100.com/#/app
                //.setAppTenantId("100009")
                .setAppTenantId(BuildConfig.DEBUG ? "100019" : "100020")
                // 默认渠道
                .setAppChannel("official")
                // 使用merchantId,初始化时不传，MainActivity中检测升级时传入
                .setAppUserId("")
                .setAppVersionName(AppUtil.getVersionName(application))
                .setAppVersionCode(AppUtil.getVersionCode(application))
                // 应用图标
                .setAppLauncher(BuildConfig.DEBUG ? R.drawable.logo_debug : R.drawable.logo)
                // 对接自己app的埋点工具类
                .setMonitorListener(XyyIoUtil::track);
    }

    public void initJgAnalysys(Application application) {
        if (isAgreedPrivacy()) {
            ReportManager.getInstance().init(application);
        }
    }

    private void initPushManagerInternal(Context context) {
        PushManager.register(context);
    }

    // 科大讯飞
    public void initVoice() {
        if (!isAgreedPrivacy()) {
            return;
        }
        if (Looper.getMainLooper() == Looper.myLooper()) {
            // 主线程
            SmartExecutorManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    initVoiceInternal();
                }
            });
        } else {
            initVoiceInternal();
        }
    }

    private void initVoiceInternal() {
        // 大坑点，讯飞语音没有做定位权限适配，但是默认申请了权限，部分手机会直接闪退
        Setting.setLocationEnable(false);
        SpeechUtility.createUtility(BaseYBMApp.getAppContext(), SpeechConstant.APPID + SPEECH_KEY);//讯飞语音
    }


    /**
     * 设置pushtoken
     */
    public void initPushToken(Context context) {
        if (!isAgreedPrivacy()) {
            return;
        }
        YbmPushUtil.registerPushtoken(context);
        if (XyyPushClientManager.OPPO == XyyPushClientManager.getInstance().getPhoneType(context)) {//OPPO默认安装完应用通知权限关闭需申请权限
            XyyPushClientManager.getInstance().requestNotificationPermission(context);
        }
    }

    public void installPingAn(Application application) {
        Helper.install(application);
    }

    /**
     * 初始化平安
     */
    public void initPingAn(Application application) {
        if (!isAgreedPrivacy()) {
            return;
        }
        KybSdk.init(application, KybSdk.PROD, "wx5766ec723a326dff", new KybInitListener() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onFailed(String s) {

            }
        });
    }

    /**
     * 初始化waf
     * @return
     */
    public void initWaf(Application application) {
        if (XyyWafManager.Companion.getInstance() == null) return;
        if (isAgreedPrivacy()) {
            XyyWafManager.Companion.getInstance().initForAllInfo(application);
        }
    }

    /**
     * 初始化埋点
     * @param application
     */
    public void initXyyReport(Application application) {
        if (isAgreedPrivacy()) {
            XyyReportManager.init(application);
        }
    }


    //设置超链接文字
    private SpannableString getClickableSpan(ClickableSpanCallback clickableSpanCallback) {
        SpannableString spanStr = new SpannableString("尊敬的用户您好，欢迎使用药帮忙APP！\n" +
                "\n" +
                "请您在使用我们的产品/服务时，务必仔细阅读《药帮忙用户服务协议》及《药帮忙隐私政策》。在阅读《药帮忙用户服务协议》时请您重点关注加粗字体标识出的关于药帮忙及您重大权益的规则，该等规则可能涉及相关方的责任免除或限制、法律适用与争议解决条款。在阅读《药帮忙隐私政策》时请您重点关注涉及药帮忙收集、使用、保护您个人信息等加粗的条款。\n" +
                "\n" +
                "请您在使用药帮忙APP之前确认对以上协议的内容及各项服务均已知晓、理解并接受，并已同意将其作为确定双方权利义务的依据。");
        //设置文字的单击事件
        spanStr.setSpan(new ClickableSpan() {

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                //设置文件颜色
                ds.setColor(UiUtils.getColor(R.color.color_00B377));
                //设置下划线
                ds.setUnderlineText(false);
            }

            @Override
            public void onClick(View widget) {
//                RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + AppNetConfig.CLAUSE);
                clickableSpanCallback.onClickServiceProtocol();
            }
        }, 42, 42 + 11, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        spanStr.setSpan(new ClickableSpan() {

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                //设置文件颜色
                ds.setColor(UiUtils.getColor(R.color.color_00B377));
                //设置下划线
                ds.setUnderlineText(false);
            }

            @Override
            public void onClick(View widget) {
                clickableSpanCallback.onClickPrivacyPolicy();
            }
        }, 54, 63, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanStr;
    }

    public interface ClickableSpanCallback {
        void onClickServiceProtocol();
        void onClickPrivacyPolicy();
    }
}
