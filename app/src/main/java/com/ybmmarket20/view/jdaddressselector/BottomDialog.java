package com.ybmmarket20.view.jdaddressselector;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;

import com.ybmmarket20.R;
import com.ybmmarket20.view.jdaddressselector.utils.Dev;


public class BottomDialog extends Dialog {
    private AddressSelector selector;

    public BottomDialog(Context context) {
        super(context, R.style.bottom_dialog);
        init(context);
    }

    public BottomDialog(Context context, int themeResId) {
        super(context, themeResId);
        init(context);
    }

    public BottomDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        init(context);
    }

    private void init(Context context) {
        selector = new AddressSelector(context);
        setContentView(selector.getView());

        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = Dev.dp2px(context, 350);
        window.setAttributes(params);

        window.setGravity(Gravity.BOTTOM);
    }

    public void setOnAddressSelectedListener(OnAddressSelectedListener listener) {
        this.selector.setOnAddressSelectedListener(listener);
    }

    public void setAddressProvider(AddressProvider listener) {
        this.selector.setAddressProvider(listener);
    }


    public static BottomDialog show(Context context) {
        return show(context, null);
    }

//    public static BottomDialog show(Context context, OnAddressSelectedListener listener) {
//        BottomDialog dialog = new BottomDialog(context, R.style.bottom_dialog);
//        dialog.selector.setOnAddressSelectedListener(listener);
//        dialog.show();
//
//        return dialog;
//    }

    public static BottomDialog show(Context context, AddressProvider listener) {
        BottomDialog dialog = new BottomDialog(context, R.style.bottom_dialog);
        dialog.selector.setAddressProvider(listener);
        dialog.show();

        return dialog;
    }

    @Override
    public void dismiss() {
        if (selector != null) {
            selector.closeDB();
        }
        super.dismiss();
    }

}
