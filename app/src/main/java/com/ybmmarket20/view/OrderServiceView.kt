package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.viewmodel.OrderDetailViewModel
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.view_order_service.view.ivAptitudeDownloadArrow
import kotlinx.android.synthetic.main.view_order_service.view.ivElectronicDeliveryFormArrow
import kotlinx.android.synthetic.main.view_order_service.view.ivInvoiceArrow
import kotlinx.android.synthetic.main.view_order_service.view.ivWarehouseHandoverFormArrow
import kotlinx.android.synthetic.main.view_order_service.view.llAptitudeDownload
import kotlinx.android.synthetic.main.view_order_service.view.llContract
import kotlinx.android.synthetic.main.view_order_service.view.llElectronicDeliveryForm
import kotlinx.android.synthetic.main.view_order_service.view.llShoppingGoldDescribe
import kotlinx.android.synthetic.main.view_order_service.view.llWarehouseHandoverForm
import kotlinx.android.synthetic.main.view_order_service.view.tvAptitudeDownloadContent
import kotlinx.android.synthetic.main.view_order_service.view.tvContractCheck
import kotlinx.android.synthetic.main.view_order_service.view.tvElectronicDeliveryFormContent
import kotlinx.android.synthetic.main.view_order_service.view.tvInvoiceContent
import kotlinx.android.synthetic.main.view_order_service.view.tvShoppingGoldDescribeCheck
import kotlinx.android.synthetic.main.view_order_service.view.tvWarehouseHandoverFormContent


/**
 * 订单服务
 */
class OrderServiceView(context: Context?, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    companion object {
        //资质状态申请（pop）
        const val APTITUDE_STATUS_POP_APPLY = 1
        //资质状态申请（pop）
        const val APTITUDE_STATUS_POP_CHECK = 2
        //资质状态(自营)
        const val APTITUDE_STATUS_SELF = 3

    }

    private var mOrderDetailViewModel: OrderDetailViewModel? = null
    private var mPaymentGoodsViewModel: PaymentGoodsViewModel? = null
    private var isObserver = false

    init {
        View.inflate(getContext(), R.layout.view_order_service, this)
    }

    /**
     * 设置数据
     */
    fun setData(orderDetailBean: CheckOrderDetailBean, orderDetailViewModel: OrderDetailViewModel, paymentGoodsViewModel: PaymentGoodsViewModel, guideCallback: (()->Unit)?) {
        mOrderDetailViewModel = orderDetailViewModel
        mPaymentGoodsViewModel = paymentGoodsViewModel
        showAfterSalesGuide(orderDetailBean, guideCallback)
        setInvoice(orderDetailBean)
        setWarehouseHandoverForm(orderDetailBean)
        setElectronicDeliveryForm(orderDetailBean)
        setContract(orderDetailBean)
        setAptitudeDownload(orderDetailBean)
        setShoppingGoldDescribe(orderDetailBean)
        if (!isObserver) {
            isObserver = true
            setObserver(orderDetailBean)
        }
    }

    /**
     * 资质下载
     */
    private fun setAptitudeDownload(orderDetailBean: CheckOrderDetailBean) {
        if (orderDetailBean.status == 2 || orderDetailBean.status == 3) {
            llAptitudeDownload.visibility = View.VISIBLE
        } else {
            llAptitudeDownload.visibility = View.GONE
        }
        val clickListener :(View) -> Unit = {
            val aptitudeStatus = if (orderDetailBean.credentialAsStateName.isNullOrEmpty()) {
              APTITUDE_STATUS_SELF
            } else if(isApplyLicenseAfterSales(orderDetailBean)) APTITUDE_STATUS_POP_APPLY else APTITUDE_STATUS_POP_CHECK
            RoutersUtils.open("ybmpage://downloadrelatedaptitude/${orderDetailBean.orgId}/${orderDetailBean.origName}/${orderDetailBean.id}/${orderDetailBean.orderNo}/${orderDetailBean.mainShopCode}/$aptitudeStatus/${orderDetailBean.credentialAfterSalesNo}")
        }
        tvAptitudeDownloadContent.setOnClickListener(clickListener)
        ivAptitudeDownloadArrow.setOnClickListener(clickListener)
    }

    /**
     * 展示售后引导
     */
    private fun showAfterSalesGuide(orderDetailBean: CheckOrderDetailBean, guideCallback: (()->Unit)?) {
        if (isApplyInvoiceAfterSales(orderDetailBean)
            && isApplyLicenseAfterSales(orderDetailBean)
            && !SpUtil.getApplyAfterSaleGuide()) {
            SpUtil.setApplyAfterSaleGuide(true)
            guideCallback?.invoke()
        }
    }

    fun setObserver(orderDetailBean: CheckOrderDetailBean) {
        mOrderDetailViewModel?.formLiveData?.observe(context as BaseActivity) {listBaseBean->
            (context as BaseActivity).dismissProgress()
            if (listBaseBean != null && listBaseBean.isSuccess && listBaseBean.data != null && listBaseBean.data.isNotEmpty()) {
                if (listBaseBean.data.size == 1) {
                    val (url, formType) = listBaseBean.data[0]
                    val title: String = if (formType == 2) {
                        "仓库交接单"
                    } else {
                        "电子出库单"
                    }
                    RoutersUtils.open("ybmpage://browsepdfactivity?fileUrl=$url&formType=$formType&title=$title&orderNo=${orderDetailBean.orderNo}")
                } else {
                    FormPdfListDialog(context, listBaseBean.data, orderDetailBean.orderNo).show()
                }
            }
        }

        mOrderDetailViewModel?.afterSaleTipsLiveData?.observe(context as BaseActivity) {
            RoutersUtils.open(it)
        }
    }

    /**
     * 是否是申请发票售后
     * 使用按钮的文本判断(不严谨，与后端沟通无效)，
     * invoinceAsStateName："申请发票售后" 时显示申请按钮
     * 其他情况显示查看按钮
     */
    private fun isApplyInvoiceAfterSales(orderDetailBean: CheckOrderDetailBean): Boolean {
        return orderDetailBean.invoiceAsStateName == "申请发票售后"
    }

    /**
     * 设置发票
     */
    private fun setInvoice(orderDetailBean: CheckOrderDetailBean) {
        val isShowInvoiceButton = orderDetailBean.isShowInvoiceBtn
        ivInvoiceArrow.visibility = if (isShowInvoiceButton) View.VISIBLE else View.INVISIBLE
        tvInvoiceContent.text = orderDetailBean.billInfo
        if (isShowInvoiceButton){
            val invoiceClick: (View)->Unit = {
                RouterJump.jump2InvoiceList(orderDetailBean)
            }
            ivInvoiceArrow.setOnClickListener(invoiceClick)
            tvInvoiceContent.setOnClickListener(invoiceClick)
        }
    }

    /**
     * 是否是申请发票售后
     * 使用按钮的文本判断(不严谨，与后端沟通无效)，
     * credentialAsStateName："申请资质售后" 时显示申请按钮
     * 其他情况显示查看按钮
     */
    private fun isApplyLicenseAfterSales(orderDetailBean: CheckOrderDetailBean): Boolean {
        return orderDetailBean.credentialAsStateName == "申请资质售后"
    }

    /**
     * 设置仓库交接单
     */
    private fun setWarehouseHandoverForm(orderDetailBean: CheckOrderDetailBean) {
        if (orderDetailBean.isShowWarehouseHandoverForm) {
            llWarehouseHandoverForm.visibility = View.VISIBLE
        } else {
            llWarehouseHandoverForm.visibility = View.GONE
            return
        }
        val warehouseHandoverClick: (View)->Unit = {
            (context as BaseActivity).showProgress()
            mOrderDetailViewModel?.getFormPdfUrl(orderDetailBean.orderNo, "2", orderDetailBean.branchCode)
        }
        ivWarehouseHandoverFormArrow.setOnClickListener(warehouseHandoverClick)
        tvWarehouseHandoverFormContent.setOnClickListener(warehouseHandoverClick)
    }

    /**
     * 设置电子交接单
     */
    private fun setElectronicDeliveryForm(orderDetailBean: CheckOrderDetailBean) {
        if (orderDetailBean.isShowElectronicDeliveryForm) {
            llElectronicDeliveryForm.visibility = View.VISIBLE
        } else {
            llElectronicDeliveryForm.visibility = View.GONE
            return
        }
        val warehouseHandoverClick: (View)->Unit = {
            (context as BaseActivity).showProgress()
            mOrderDetailViewModel?.getFormPdfUrl(orderDetailBean.orderNo, "1", orderDetailBean.branchCode)
        }
        ivElectronicDeliveryFormArrow.setOnClickListener(warehouseHandoverClick)
        tvElectronicDeliveryFormContent.setOnClickListener(warehouseHandoverClick)
    }

    /**
     * 购销合同
     */
    private fun setContract(orderDetailBean: CheckOrderDetailBean) {
        llContract.visibility = if (orderDetailBean.contract?.showContract == true) View.VISIBLE
        else View.GONE
        tvContractCheck.setOnClickListener {
            orderDetailBean.contract?.contractUrl?.let {
                RoutersUtils.open("ybmpage://aptitudexyypdf?title=购销合同&fileType=1&orderNo=${orderDetailBean.orderNo}&pdfUrl=$it")
            }
        }
    }

    /**
     * 没有购物金说明的弹窗
     * @param content String
     */
    private fun showNoShoppingGoldDescribeDialog() {
        val dialogEx = AlertDialogEx(this.context)
        dialogEx.setMessage("暂未生成代收款说明，请稍后重试")
                .setCancelButton("确定", AlertDialogEx.OnClickListener { dialog, button -> dialog.dismiss() })
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setTitle(null).show()
    }
    /**
     * 购物金代收款说明
     */
    private fun setShoppingGoldDescribe(orderDetailBean: CheckOrderDetailBean) {
        llShoppingGoldDescribe.visibility = if (orderDetailBean.goldCollectCertificate?.showContract == true) View.VISIBLE else View.GONE
        tvShoppingGoldDescribeCheck.setOnClickListener {
            orderDetailBean.goldCollectCertificate?.contractUrl?.let {

                if (it.isEmpty()){
                    showNoShoppingGoldDescribeDialog()
                    return@setOnClickListener
                }

                var mUrl = "ybmpage://aptitudexyypdf?fileType=11&orderNo=${orderDetailBean.orderNo}&pdfUrl=$it"
                orderDetailBean.goldCollectCertificate?.contractName?.let { name->
                    if (name.isNotEmpty()){
                        mUrl += "&title=$name"
                    }
                }
                RoutersUtils.open(mUrl)
            }?: kotlin.run {
                showNoShoppingGoldDescribeDialog()
            }
        }

    }
}