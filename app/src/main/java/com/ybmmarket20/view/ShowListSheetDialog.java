package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.PlanListData;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 电子采购单添加药品-底部弹出dialog
 */

public class ShowListSheetDialog extends ShowCaptureBottomSheetDialog {


    public ShowListSheetDialog(Context context) {
        super(context);
    }

    @Override
    protected void initView() {
        super.initView();
        adapter.setEnableLoadMore(false);
        mList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData();
            }

            @Override
            public void onLoadMore() {
                adapter.setEnableLoadMore(false);
            }
        });
    }

    private void getData() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.PLAN_SCHEDULE_LIST)
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<List<PlanListData>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<PlanListData>> obj, List<PlanListData> planListBean) {

                completion();
                if (obj != null && obj.isSuccess()) {

                    if (planListBean != null && planListBean.size() > 0) {

                        List<SearchFilterBean> filterBeen = new ArrayList<>();
                        for (int i = 0; i < planListBean.size(); i++) {
                            PlanListData planListData = planListBean.get(i);
                            SearchFilterBean filterBean = new SearchFilterBean(
                                    planListData.planningName, "" + planListData.id, planListBean.size());
                            filterBeen.add(filterBean);
                        }
                        setNewData(filterBeen);
                    }
                } else {
                    notifyDataChanged();
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (!TextUtils.isEmpty(error.message)) {
                    ToastUtils.showShort(error.message);
                }
            }
        });
    }

    @Override
    public void setNewData(List<SearchFilterBean> list) {
        this.list = list;
        if (adapter != null) {
            adapter.setNewData(list);
        }
    }

    private void notifyDataChanged() {
        if (mList != null) {
            mList.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }
                }
            }, 200);
        }
    }

    private void setViewHeight(int size) {
        if (size <= 0) {
            size = 4;
        }
        if (size > 10) {
            size = 10;
        }
        setLayoutParams(ConvertUtils.dp2px(47) * size);
    }


    private void completion() {
        if (mList != null) {
            mList.setRefreshing(false);
        }
    }
}
