package com.ybmmarket20.view.homesteady

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.ROUTER_CAPTURE
import com.ybmmarket20.constant.ROUTER_VOICE_SEARCH_PRODUCT
import com.ybmmarket20.constant.ROUTER_SEARCH_PRODUCT
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.layout_home_steady_search.view.*

/**
 * <AUTHOR>
 * @date 2020-05-07
 * @description 首页搜索框头部
 */
class HomeSteadySearchView(context: Context, attr: AttributeSet) : BaseHomeSteadyView(context, attr), View.OnClickListener, IHomeSteady {

    init {
        iv_scan.setOnClickListener(this)
        iv_voice.setOnClickListener(this)
        iv_message.setOnClickListener(this)
        v_search_bg.setOnClickListener(this)
    }

    override fun getLayoutId(): Int = R.layout.layout_home_steady_search

    override fun onClick(v: View?) {
        when(v?.id) {
            //扫码
            R.id.iv_scan -> RoutersUtils.open(ROUTER_CAPTURE)
            //语音搜索
            R.id.iv_voice -> RoutersUtils.open(ROUTER_VOICE_SEARCH_PRODUCT)
            //打开消息中心
            R.id.iv_message -> Message.openMessagePage()
            //搜索框打开搜索
            R.id.v_search_bg -> RouterJump.jump2SearchPage()
        }
    }

    /**
     * 获取消息气泡view
     */
    fun getBubbleView(): TextView = findViewById(R.id.tv_home_steady_message_bubble)
}