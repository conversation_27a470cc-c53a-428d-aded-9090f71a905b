package com.ybmmarket20.view.productEdit

import android.content.Context
import com.ybmmarket20.bean.RowsBean

interface IProductEditAnalysis {

    /**
     * 加购按钮点击
     */
    fun onAddCartBtnClick(context: Context, rowsBean: RowsBean, position: Int)

    /**
     * 加号按钮点击
     */
    fun onAddBtnClick(context: Context, rowsBean: RowsBean, position: Int)

    /**
     * 减号按钮点击
     */
    fun onSubtractBtnClick(context: Context, rowsBean: RowsBean, position: Int)

    /**
     * 数字点击
     */
    fun onProductEditNumClick(context: Context, rowsBean: RowsBean, position: Int, content: String)
}