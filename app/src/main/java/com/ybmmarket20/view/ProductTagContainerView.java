package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.TagBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品标签容器组件
 * 用于显示商品的各种标签，包括图片标签和文字标签
 */
public class ProductTagContainerView extends LinearLayout {

    public ProductTagContainerView(Context context) {
        super(context);
        init();
    }

    public ProductTagContainerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ProductTagContainerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        setOrientation(LinearLayout.HORIZONTAL);
    }

    /**
     * 设置标签列表（LabelIconBean类型）
     * @param tagList 标签列表
     */
    public void setTagList(List<LabelIconBean> tagList) {
        // 清除之前的所有子视图
        removeAllViews();

        if (tagList == null || tagList.isEmpty()) {
            setVisibility(View.GONE);
            return;
        }

        setVisibility(View.VISIBLE);

        // 循环处理每个标签
        for (LabelIconBean tagBean : tagList) {
            if (tagBean.uiStyle == 3) {
                // 如果uiStyle值为3，则创建图片标签
                addImageTag(tagBean);
            } else {
                // 否则用ShopNameWithTagView创建文字标签
                addTextTag(tagBean);
            }
        }
    }

    /**
     * 设置标签列表（TagBean类型）
     * @param tagList 标签列表
     * @param maxTagCount 最大标签数量
     */
    public void setTagBeanList(List<TagBean> tagList, int maxTagCount) {
        // 清除之前的所有子视图
        removeAllViews();

        if (tagList == null || tagList.isEmpty()) {
            setVisibility(View.GONE);
            return;
        }

        setVisibility(View.VISIBLE);

        // 直接使用ShopNameWithTagView创建标签
        ShopNameWithTagView tagView = new ShopNameWithTagView(getContext());
        tagView.bindData(tagList, null, maxTagCount);

        // 设置布局参数
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        tagView.setLayoutParams(layoutParams);

        addView(tagView);
    }

    /**
     * 设置标签列表（TagBean类型，默认最大数量100）
     * @param tagList 标签列表
     */
    public void setTagBeanList(List<TagBean> tagList) {
        setTagBeanList(tagList, 100);
    }

    /**
     * 创建图片标签
     * @param tagBean 标签数据
     */
    private void addImageTag(LabelIconBean tagBean) {
        ImageView imageView = new ImageView(getContext());

        // 设置imageView的尺寸为宽度：30dp,高度为：14dp
        int widthInDp = 30;
        int heightInDp = 14;
        int widthInPx = UiUtils.dp2px(widthInDp);
        int heightInPx = UiUtils.dp2px(heightInDp);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(widthInPx, heightInPx);

        // 设置图片的显示方式
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);

        // 添加右边距
        layoutParams.rightMargin = UiUtils.dp2px(6);
        imageView.setLayoutParams(layoutParams);

        // 加载图片
        if (!TextUtils.isEmpty(tagBean.appIcon)) {
            String url = tagBean.appIcon;
            if (!url.startsWith("http")) {
                url = AppNetConfig.LORD_TAG + url;
            }

            ImageHelper.with(getContext())
                    .load(url)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate()
                    .into(imageView);
        }

        addView(imageView);
    }

    /**
     * 创建文字标签
     * @param tagBean 标签数据
     */
    private void addTextTag(LabelIconBean tagBean) {
        List<TagBean> tagList = new ArrayList<>();

        // 将LabelIconBean转换为TagBean
        TagBean tag = new TagBean();
        tag.text = tagBean.text;
        tag.textColor = tagBean.textColor;
        tag.bgColor = tagBean.bgColor;
        tag.borderColor = tagBean.borderColor;
        tag.appIcon = tagBean.appIcon;
        try {
            tag.uiStyle = tagBean.uiStyle;
        } catch (NumberFormatException e) {
            tag.uiStyle = 1; // 默认值
        }
        tagList.add(tag);

        ShopNameWithTagView tagView = new ShopNameWithTagView(getContext());
        tagView.bindData(tagList, null, 100);

        // 添加右边距
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.rightMargin = UiUtils.dp2px(4);
        tagView.setLayoutParams(layoutParams);

        addView(tagView);
    }
}
