package com.ybmmarket20.view.operationposition.content.goodsState

import android.view.View
import androidx.core.content.ContextCompat
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarketkotlin.adapter.SpellGroupPopWindow
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus

/**
 * 拼团
 */
class SpellGroupGoodsState(itemView: View) : GoodsViewTimeState(itemView) {

    override fun handleData(
            rowsBean: RowsBean,
            parentPosition: Int,
            isShowShop: Boolean,
            flowData: BaseFlowData?,
            position: Int
    ) {
        super.handleData(rowsBean, parentPosition, isShowShop, flowData,position)
        when (rowsBean.isGroupBookingRow) {
            is GoodListBindItemStatus.SpellGroupStatusPreHot -> {
                //拼团预热
                getPriceView().visibility = View.VISIBLE
                getPriceView().text = if (rowsBean.actPt?.preheatShowPrice == 0) getEmptyPrice() else getNormalPrice(if (rowsBean.actPt?.stepPriceStatus == 1) {
                    //阶梯价
                    rowsBean.actPt.minSkuPrice?: "0"
                } else {
                    "${rowsBean.actPt?.assemblePrice?: 0}"
                })
                getPriceView().setTextColor(ContextCompat.getColor(getContext(), R.color.color_03B955))
                getSpellGroupPreHotBtn().visibility = View.VISIBLE
                getSpellGroupBtn().isEnabled = false

//                getOPGoodsTime().visibility = View.VISIBLE
//                setTitleText("距开始")
//                setTitleBg(R.drawable.shape_op_time_title_prehot)
//                setClTimeBg(R.drawable.shape_op_time_bg_prehot)
//                setTimeSolidAll(R.drawable.shape_op_time_item_prehot)
//                startCountDown(rowsBean)
            }

            is GoodListBindItemStatus.SpellGroupStatusSellOut -> {
                //拼团售罄
                getPriceView().visibility = View.VISIBLE
//                getPriceView().text = getNormalPrice("${rowsBean.fob}")
                //拼团价格
                val price = if (rowsBean.actPt?.stepPriceStatus == 1) {
                    //阶梯价
                    rowsBean.actPt.minSkuPrice?: "0"
                } else {
                    "${rowsBean.actPt?.assemblePrice?: 0}"
                }
                getPriceView().text = getNormalPrice(price)
            }

            else -> {
                //拼团
                getPriceView().visibility = View.VISIBLE
                getSpellGroupBtn().visibility = View.VISIBLE
                getSpellGroupBtn().setOnClickListener {
                    productClickListener?.invoke(true,"参团",null)
                    trackClick()
                    handleSpellGroup(rowsBean, flowData)
                }
                //拼团价格
                val price = if (rowsBean.actPt?.stepPriceStatus == 1) {
                    //阶梯价
                    rowsBean.actPt.minSkuPrice?: "0"
                } else {
                    "${rowsBean.actPt?.assemblePrice?: 0}"
                }
                getPriceView().text = getNormalPrice(price)
            }
        }
    }

    override fun getLeftTime(rowsBean: RowsBean): Long {
        val localDiff: Long = System.currentTimeMillis() - (rowsBean.actPt?.responseLocalTime ?: 0L)
        return (rowsBean.actPt?.surplusTime ?: 0L) * 1000 - localDiff
    }

    override fun getClickItemType(): String = OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_SPELL_GROUP

    /**
     * 拼团
     */
    private fun handleSpellGroup(rowsBean: RowsBean, flowData: BaseFlowData?) {
        if (rowsBean.actPt != null && rowsBean.actPt.isApplyListShowType) {
            rowsBean.jgTrackBean = jgTrackBean
            val mPopWindowSpellGroup = SpellGroupPopWindow(
                getContext(),
                rowsBean,
                rowsBean.actPt,
                false,
                mIsList = true,
                flowData,
                    jgTrackBean,
                    jgPageListCommonBean
            )
            mPopWindowSpellGroup.show(itemView)
        } else {
            var mUrl = "ybmpage://productdetail/" + rowsBean.id
            mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                    Pair<String,Any>(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:""),
                    Pair<String,Any>(IntentCanst.JG_REFERRER,jgTrackBean?.jgReferrer?:""),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,jgTrackBean?.jgReferrerTitle?:""),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,jgTrackBean?.jgReferrerModule?:""),
            ))
            RoutersUtils.open(mUrl)
//            openUrl(
//                , flowData
//            )
        }
    }
}