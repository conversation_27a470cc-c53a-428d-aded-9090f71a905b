package com.ybmmarket20.search

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SuggestListBeanV2
import com.ybmmarket20.bean.SuggestListItem
import com.ybmmarket20.bean.SuggestListItemSelected
import com.ybmmarket20.bean.SuggestShopBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.highLightText
import com.ybmmarket20.constant.AppNetConfig.ADD_HISTORY_SUGGEST
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import java.net.URLEncoder


/**
 * 搜索的推荐 新的样式增加单条商品信息
 */
class SuggestNewPopWindowNewV2(val context: Context, private val url: String, private val params: RequestParams?, private val token: View) {
    var dismissShopAndProduct: Boolean = false
    private var popwindow: PopupWindow? = null
    private var rowsBean: RowsBean? = null

    private var suggestProductList: MutableList<RowsBean> = arrayListOf()
    private var suggestShopList: MutableList<SuggestShopBean> = arrayListOf()
    private var suggestTextList: MutableList<SuggestListItem> = arrayListOf()

    lateinit var contentView: LinearLayout
    lateinit var listView: RecyclerView
    lateinit var bgView: View

    lateinit var suggestProductAdapter: GoodListAdapterNew
    lateinit var suggestTextAdapter: SuggestTextAdapter
    lateinit var suggestShopAdapter: SuggestShopAdapter
    lateinit var concatAdapter: ConcatAdapter

    var prePageSource: String? = null
    var pageUrl: String? = null

    var merchantid: String? = null
    var listener: ItemClickListener? = null
    var onTouchListener: OnTouchListener? = null
    private var cancelHandler = false

    private var lastTime: Long = 0
    private val diffTime: Long = 200
    val allItemList = mutableListOf<Any>()

    var suggestBean: SuggestListBeanV2? = null

    var mKeyWord = ""

    var onShowCallback: ((Boolean) -> Unit)? = null

    var mShopClickCallBack:((SuggestShopBean?,Int)->Unit)? = null
        set(value) {
            field = value
            suggestShopAdapter.shopClickCallBack = value
        }

    init {
        init(context)
    }

    private fun initPop(token: View) {
        popwindow = PopupWindow(contentView,
                token.width, WindowManager.LayoutParams.MATCH_PARENT, false)
        popwindow?.setBackgroundDrawable(ColorDrawable(Color.parseColor("#ee222222")))
//        popwindow?.isOutsideTouchable = true
        popwindow?.animationStyle = R.style.PopupWindowAnimation
        popwindow?.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        popwindow?.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING
        popwindow?.setTouchInterceptor(onTouchListener)
        popwindow?.setOnDismissListener {
            onShowCallback?.invoke(false)
        }
    }

    private fun init(context: Context) {

        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        contentView = inflater.inflate(R.layout.pop_list_suggest, null, false) as LinearLayout
        listView = contentView.findViewById(R.id.listView)
        suggestProductAdapter = GoodListAdapterNew(R.layout.item_goods_new, suggestProductList).apply {
            isFromSearchSug = true
            resourceViewTrackListener = { rowsBean, i,_ ->
//                context.jgTrackSearchResourceView(rowsBean.showName ?:"",i,JGTrackManager.TrackSearchIntermediateState.SEARCH_MODULE_SUG)
            }
        }
        suggestShopAdapter = SuggestShopAdapter(R.layout.item_suggest_shopname, suggestShopList).apply {
            this.shopClickCallBack = <EMAIL>
        }
        suggestTextAdapter = SuggestTextAdapter(R.layout.list_item_suggest_v2, suggestTextList)

        concatAdapter = ConcatAdapter(listOf(suggestProductAdapter, suggestShopAdapter, suggestTextAdapter))
        listView.adapter = concatAdapter

        listView.layoutManager = WrapLinearLayoutManager(context)
    }

    fun setPopFocusable(focusable: Boolean) {
        popwindow?.isFocusable = focusable
    }

    fun setPopOutsideTouchable(touchable: Boolean) {
        popwindow?.isOutsideTouchable = touchable
    }

    private val sugestHandler: BaseResponse<*> = object : BaseResponse<SuggestListBeanV2>() {
        override fun onSuccess(content: String, obj: BaseBean<SuggestListBeanV2>?, suggestBean: SuggestListBeanV2?) {
            if (obj != null && obj.isSuccess && !cancelHandler) {
                suggestBean?.let { show(token, suggestBean) }
            }
        }
    }

    fun suggest(keyWord: String) {
        mKeyWord = keyWord
        if (System.currentTimeMillis() - lastTime >= diffTime) {
            getSuggestList(keyWord, sugestHandler)
        }
        lastTime = System.currentTimeMillis()
    }

    private fun getSuggestList(keyword: String, handler: BaseResponse<*>) {
        if (!cancelHandler) {
            if (params != null) {
                merchantid = SpUtil.getMerchantid()
                params.put("merchantId", merchantid)
                params.put("skuName", keyword)
                params.put("keyword", keyword)
                if (prePageSource != null) {
                    params.put("pageSource", "${prePageSource}_conebox_e${URLEncoder.encode(URLEncoder.encode(keyword, "UTF-8"), "UTF-8")}")
                    params.put("nsid", "")
                    params.put("listoffset", "")
                    params.put("listdata", "")
                    params.put("pageurl", pageUrl)
                }
            }
            HttpManager.getInstance().post(url, params, handler)
        } else {
            if (isShow) {
                suggestBean?.let { updateData(it) }
            }
        }
    }

    private fun show(token: View, suggestBean: SuggestListBeanV2) {
        if (popwindow == null) {
            initPop(token)
            this.suggestBean = suggestBean
        }
        try {
            if (isShow) {
                updateData(suggestBean)
                return
            }
        } catch (e: Exception) {
            return
        }
        try {
            if (updateData(suggestBean)) {
                if (Build.VERSION.SDK_INT >= 24) {
                    val location = IntArray(2)
                    token.getLocationOnScreen(location)
                    // 7.1 版本处理
                    if (Build.VERSION.SDK_INT == 25) {
                        //【note!】Gets the screen height without the virtual key
                        val wm = popwindow?.contentView?.context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                        val screenHeight = wm.defaultDisplay.height
                        popwindow?.height = screenHeight - location[1] - token.height
                    }
                    popwindow?.showAtLocation(token, Gravity.NO_GRAVITY, 0, location[1] + token.height)
                    onShowCallback?.invoke(true)
                } else {
                    popwindow?.showAsDropDown(token, 0, 0)
                    onShowCallback?.invoke(true)
                }
                popwindow?.showAsDropDown(token, 0, 0)
                onShowCallback?.invoke(true)
            }
        } catch (e: Exception) {
            return
        }
        popwindow?.update()
    }

    private fun updateData(suggestBean: SuggestListBeanV2): Boolean {
        allItemList.clear()
        if (dismissShopAndProduct==false){
            suggestProductList.clear()
            suggestBean.csuInfo?.let {
                suggestProductList.add(it.apply {
                    searchKeyword = mKeyWord
                })
            }
            allItemList.addAll(suggestProductList)
        }

        if (!dismissShopAndProduct){
            suggestShopList.clear()
            suggestBean.suggestShopList?.let { suggestShopList.addAll(it.onEach { suggestShopBean ->
                suggestShopBean.searchKeyword = mKeyWord
            }) }
            allItemList.addAll(suggestShopList)
        }

        suggestTextList.clear()
        suggestBean.suggestList?.let {
            suggestTextList.addAll(it.onEach { suggestListItem ->
                suggestListItem.searchKeyword = mKeyWord
            })
            allItemList.addAll(suggestTextList)
        }

        suggestProductAdapter.notifyDataSetChanged()
        suggestShopAdapter.notifyDataSetChanged()
        suggestTextAdapter.notifyDataSetChanged()

        if (suggestTextList.isEmpty() && suggestShopList.isEmpty() && suggestProductList.isEmpty()) {
            dismiss()
            return false
        }
        popwindow?.height = bgView.height
        return true
    }

    fun dismiss() {
        popwindow?.dismiss()
    }

    val isShow: Boolean = popwindow?.isShowing ?: false

    fun isPopShow(): Boolean = if (popwindow == null) false
        else popwindow!!.isShowing
    fun cancelHandler(cancel: Boolean) {
        cancelHandler = cancel
    }

    fun setItemClickListener(listener: ItemClickListener?) {
        this.listener = listener
    }


    inner class SuggestTextAdapter(layoutResId: Int, data: MutableList<SuggestListItem>?) : YBMBaseAdapter<SuggestListItem>(layoutResId, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SuggestListItem?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->

                holder.getView<TextView>(R.id.tvName)?.text = highLightText(bean.suggestion?:"",bean.searchKeyword?:"",ContextCompat.getColor(mContext,R.color.color_00b955))
//                holder.setOnClickListener(R.id.tvName) {
//                    listener?.onItemClick(
//                        SuggestListItemSelected(bean.suggestion?: "", null, null) ,
//                        rowsBean?.id ?: -1,
//                        holder.position
//                    )
//                }
                holder.itemView.setOnClickListener {
                    listener?.onItemClick(
                        SuggestListItemSelected(bean.suggestion?: "", null, null) ,
                        rowsBean?.id ?: -1,
                        holder.position
                    )
                }
                //没有标签数据就不向下执行
                val llTags = holder.getView<LinearLayout>(R.id.llTags)
                val llTagsWidth = ScreenUtils.getScreenWidth(mContext) - ScreenUtils.dip2px(mContext, 20f)
                llTags.removeAllViews()
                if (bean.leftLabels.isEmpty() && bean.rightLabels.isEmpty()) return@whenAllNotNull
                val paint = Paint()
                paint.textSize = ScreenUtils.dip2px(mContext, 14f).toFloat()
                val textWidth = paint.measureText(bean.suggestion)
                if (textWidth >= llTagsWidth) return@whenAllNotNull
                var curWidth = textWidth
                bean.leftLabels.forEach {
                    paint.textSize = ScreenUtils.dip2px(mContext, 12f).toFloat()
                    val tagWidth = paint.measureText(it.name)
                    curWidth += tagWidth + ScreenUtils.dip2px(mContext, 19f)
                    if (curWidth >= llTagsWidth) return@whenAllNotNull
                    XyyIoUtil.track("search_sug_exposure", hashMapOf(
                        "query" to bean.suggestion,
                        "sug" to it.name,
                        "label_type" to "traditional_medicine"
                    ))
                    val tagView = addTag(mContext, llTags, it.name?: "", R.color.color_A14E21, R.color.color_FFF3E2)
                    tagView.setOnClickListener { _ ->
                        XyyIoUtil.track("search_sug_click", hashMapOf(
                            "query" to bean.suggestion,
                            "sug" to it.name,
                            "label_type" to "traditional_medicine"
                        ))
                        listener?.onItemClick(
                            SuggestListItemSelected(bean.suggestion?: "", it, null) ,
                            rowsBean?.id ?: -1,
                            holder.position
                        )
                    }
                }
                bean.rightLabels.forEach {
                    paint.textSize = ScreenUtils.dip2px(mContext, 12f).toFloat()
                    val tagWidth = paint.measureText(it.name)
                    curWidth += tagWidth + ScreenUtils.dip2px(mContext, 19f)
                    if (curWidth >= llTagsWidth) return@whenAllNotNull
                    val tagView = addTag(mContext, llTags, it.name?: "", R.color.color_292933, R.color.color_F7F7F8)
                    XyyIoUtil.track("search_sug_exposure", hashMapOf(
                        "query" to bean.suggestion,
                        "sug" to it.name,
                        "label_type" to "spec"
                    ))
                    tagView.setOnClickListener { _ ->
                        XyyIoUtil.track("search_sug_click", hashMapOf(
                            "query" to bean.suggestion,
                            "sug" to it.name,
                            "label_type" to "spec"
                        ))
                        listener?.onItemClick(
                            SuggestListItemSelected(bean.suggestion?: "", it, null) ,
                            rowsBean?.id ?: -1,
                            holder.position
                        )
                    }
                }

//                holder.itemView.context.jgTrackSearchResourceView(bean.suggestion ?: "",holder.bindingAdapterPosition,JGTrackManager.TrackSearchIntermediateState.SEARCH_MODULE_SUG)

            }
        }

        private fun addTag(context: Context, parent: LinearLayout, suggestText: String, textColor: Int, bgColor: Int): TextView {
            return TextView(context).apply {
                textSize = 12f
                text = suggestText
                setTextColor(UiUtils.getColor(textColor))
                val shapeDrawable = GradientDrawable()
                shapeDrawable.setColor(UiUtils.getColor(bgColor))
                shapeDrawable.cornerRadius = UiUtils.dp2px(2).toFloat()
                background = shapeDrawable
                maxLines = 1
                gravity = Gravity.CENTER
                setPadding(UiUtils.dp2px(5), 0, UiUtils.dp2px(5), 0)
                val lp = LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ScreenUtils.dip2px(mContext, 24f))
                lp.leftMargin = UiUtils.dp2px(10)
                layoutParams = lp
            }.also(parent::addView)
        }
    }

    inner class SuggestShopAdapter(layoutResId: Int, data: MutableList<SuggestShopBean>?) : YBMBaseAdapter<SuggestShopBean>(layoutResId, data) {
        var shopClickCallBack:((SuggestShopBean?, Int)->Unit)? = null
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SuggestShopBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                holder.itemView.setOnClickListener {
                    bean.skipUrl?.let {
                        val params = RequestParams().apply {
                            put("keyword", bean.shopName)
                            put("shopCode", bean.shopCode)
                            put("skipUrl", bean.skipUrl)
                        }
                        HttpManager.getInstance().post(ADD_HISTORY_SUGGEST, params, object : BaseResponse<Any>() {})
                        RoutersUtils.open(it)
                        shopClickCallBack?.invoke(bean,holder.adapterPosition)
                    }


                }
//                holder.itemView.context.jgTrackSearchResourceView(t?.shopName ?:"",holder.bindingAdapterPosition,JGTrackManager.TrackSearchIntermediateState.SEARCH_MODULE_SUG)

                holder.getView<TextView>(R.id.tv_shop_name)?.text = highLightText(t?.shopName?:"",bean.searchKeyword?:"", ContextCompat.getColor(mContext,R.color.color_00b955))

                //sug店铺标签
                val tagView = holder.getView<ShopNameWithTagView>(R.id.snwtv_sug_shop_tag)
                if (!bean.sugShopTags.isNullOrEmpty()) {
                    tagView.bindData(bean.sugShopTags, null, Int.MAX_VALUE)
                    tagView.visibility = View.VISIBLE
                } else tagView.visibility = View.GONE
                //sug店铺描述
                val tvSugShopDes = holder.getView<TextView>(R.id.tv_sug_shop_des)
                if (!bean.getSugShopDes().isNullOrEmpty()) {
                    tvSugShopDes.text = bean.getSugShopDes()
                    tvSugShopDes.visibility = View.VISIBLE
                } else tvSugShopDes.visibility = View.GONE
            }
        }
    }

    interface ItemClickListener {
        fun onItemClick(selectedItem: SuggestListItemSelected, id: Long, position: Int)
    }

    private fun getNavigationBarHeight(): Int {
        val resources: Resources = (context as Activity).getResources()
        val resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android")
        val height = resources.getDimensionPixelSize(resourceId)
        return height
    }

    private fun getStatusBarHeight(): Int {
        val resources: Resources = (context as Activity).getResources()
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        val height = resources.getDimensionPixelSize(resourceId)
        return height
    }

}