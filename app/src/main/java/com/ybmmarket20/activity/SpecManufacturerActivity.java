package com.ybmmarket20.activity;


import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ManufacturerBean;
import com.ybmmarket20.bean.SpecBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.DividerLine;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 电子计划单-搜索- 规格（厂家）
 */
public class SpecManufacturerActivity extends BaseActivity {

    public static final int FROM_SPEC = 1;
    public static final int FROM_MANU = 2;
    public static final String INTENT_MANU = "intent_manu";
    public static final String INTENT_NAME = "intent_name";
    public static final String INTENT_TYPE = "intent_type";
    public static final String INTENT_SPEC = "intent_spec";
    public static final String INTENT_FROM = "intent_from";

    @Bind(R.id.crv_list)
    CommonRecyclerView crvList;

    private int mFrom, mType;
    private List<String> mData = new ArrayList<>();
    private String mManufacturer, mProductName, mSpec;

    @Override
    protected void initData() {
        mFrom = getIntent().getIntExtra(INTENT_FROM, -1);
        mType = getIntent().getIntExtra(INTENT_TYPE, 1);
        mManufacturer = getIntent().getStringExtra(INTENT_MANU);
        mProductName = getIntent().getStringExtra(INTENT_NAME);
        mSpec = getIntent().getStringExtra(INTENT_SPEC);

        if (mFrom == FROM_SPEC) {
            setTitle("规格");
            mSelectAdapter.addHeaderView(createHeader("全部规格"));
        } else if (mFrom == FROM_MANU) {
            mSelectAdapter.addHeaderView(createHeader("全部厂家"));
            setTitle("厂家");
        }
        setLeft(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        }, R.drawable.icon_left_close);

        crvList.setShowAutoRefresh(false);
        crvList.setRefreshEnable(false);
        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xffeeeeee);
        crvList.addItemDecoration(divider);
        crvList.setAdapter(mSelectAdapter);
        crvList.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_no_data, "没有相关的数据");
        getData();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_spec_manufacture;
    }

    private View createHeader(final String text) {
        View header = LayoutInflater.from(this).inflate(R.layout.layout_spec_manufacturer_header, null);
        TextView tv = (TextView) header.findViewById(R.id.tv_header);
        tv.setText(text);
        tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                activityResult("");
            }
        });
        return header;
    }

    public void activityResult(String text) {
        Intent intent = new Intent();
        intent.putExtra("select", text);
        setResult(RESULT_OK, intent);
        finish();
    }

    private void getData() {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        if (!TextUtils.isEmpty(mProductName)) {
            params.put("productName", mProductName);
        }
        params.put("type", String.valueOf(mType));
        if (mFrom == FROM_SPEC) {
            if (!TextUtils.isEmpty(mManufacturer)) {
                params.put("manufacturer", mManufacturer);
            }
        } else if (mFrom == FROM_MANU) {
            if (!TextUtils.isEmpty(mSpec)) {
                params.put("spec", mSpec);
            }
        }

        String url = mFrom == FROM_SPEC ? AppNetConfig.REPLENISH_SPEC : AppNetConfig.REPLENISH_MANUFACTURER;
        if (mFrom == FROM_SPEC) {
            showProgress();
            HttpManager.getInstance().post(url, params, new BaseResponse<SpecBean>() {
                @Override
                public void onSuccess(String content, BaseBean<SpecBean> obj, SpecBean data) {
                    dismissProgress();
                    if (obj == null || obj.getData() == null || !obj.isSuccess() || obj.getData().getSpecList() == null) {
                        ToastUtils.showShort("没有相关数据");
                        return;
                    }
                    for (int i = 0; i < obj.getData().getSpecList().size(); i++) {
                        mData.add(obj.getData().getSpecList().get(i).getSpec());
                    }
                    if (crvList != null && mSelectAdapter != null) {
                        mSelectAdapter.setNewData(mData);
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    super.onFailure(error);
                    dismissProgress();
                }
            });
        } else if (mFrom == FROM_MANU) {
            showProgress();
            HttpManager.getInstance().post(url, params, new BaseResponse<ManufacturerBean>() {
                @Override
                public void onSuccess(String content, BaseBean<ManufacturerBean> obj, ManufacturerBean data) {
                    dismissProgress();
                    if (obj == null || obj.getData() == null || !obj.isSuccess() || obj.getData().getManufacturerList() == null) {
                        return;
                    }
                    for (int i = 0, len = obj.getData().getManufacturerList().size(); i < len; i++) {
                        mData.add(obj.getData().getManufacturerList().get(i).getManufacturer());
                    }
                    if (crvList != null && mSelectAdapter != null) {
                        mSelectAdapter.setNewData(mData);
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    super.onFailure(error);
                    dismissProgress();
                }
            });
        }
    }

    private YBMBaseAdapter<String> mSelectAdapter = new YBMBaseAdapter<String>(R.layout.item_single_text, mData) {
        @Override
        protected void bindItemView(YBMBaseHolder baseViewHolder, final String s) {
            baseViewHolder.setText(R.id.text, s);
            baseViewHolder.getView(R.id.text).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    activityResult(s);
                }
            });
        }
    };

    @Override
    public String getPageName() {
        if (mFrom == FROM_SPEC) {
            return XyyIoUtil.PAGE_MEELECTRONICPLANSEARCHS;
        } else {
            return XyyIoUtil.PAGE_MEELECTRONICPLANSEARCHM;
        }
    }
}
