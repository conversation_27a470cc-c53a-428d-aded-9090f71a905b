package com.ybmmarket20.adapter

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.util.SparseArray
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.StandardInfo
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.flowDataPageListStandardGoodsClickForFeed
import com.ybmmarket20.utils.analysis.flowDataPageListStandardGoodsExposureForFeed
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 标品适配器
 */
class StandardGoodsListAdapter(context: Context, layoutId: Int, list: MutableList<StandardInfo>?) :
    YBMBaseAdapter<StandardInfo>(layoutId, list) {

    private val traceShopData = SparseArray<SparseArray<String>>()
    var tabIndex = 0
    init {
        if (mContext == null) mContext = context
    }
    var flowData: BaseFlowData? = null

    public override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: StandardInfo?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + bean.imageUrl,
                holder.getView(R.id.ivStandardGoods)
            )
            holder.setText(R.id.tvStandardGoodsTitle, bean.showName)
            holder.setText(R.id.tvStandardGoodsManufacturer, bean.manufacturer)
            holder.setText(R.id.tvStandardGoodsSaleCount, " ${bean.saleCountStr}")
            holder.itemView.setOnClickListener {
                RoutersUtils.open("ybmpage://searchproduct?isFromHome=1&isOftenBuyFrom=1&masterStandardProductId=${bean.masterStandardProductId ?: ""}&originalShowName=${bean.originalShowName ?: ""}")
                flowDataPageListStandardGoodsClickForFeed(flowData, hashMapOf(
                    "sku_id" to (bean.masterStandardProductId?: "")
                ))
            }
            val tvPrice = holder.getView<TextView>(R.id.tvStandardGoodsPrice)
            tvPrice.text = if (bean.maxPrice == bean.minPrice) {
                SpannableStringBuilder("￥").append(getPriceSpannableBuilder(bean.minPrice))
            } else {
                SpannableStringBuilder("￥")
                    .append(getPriceSpannableBuilder(bean.minPrice))
                    .append(SpannableStringBuilder(" ~ ").apply {
                        setSpan(
                            AbsoluteSizeSpan(
                                11,
                                true
                            ), 0, 3, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    })
                    .append(getPriceSpannableBuilder(bean.maxPrice))
            }.apply {
                setSpan(AbsoluteSizeSpan(11, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }

            if (traceShopData[holder.bindingAdapterPosition] == null) {
                traceShopData.put(holder.bindingAdapterPosition, SparseArray())
                flowDataPageListStandardGoodsExposureForFeed(flowData, hashMapOf(
                    "sku_id" to (bean.masterStandardProductId?: ""),
                    "tab_position" to "${tabIndex + 1}",
                    "offset" to "${holder.bindingAdapterPosition + 1}"
                ))
            }
        }
    }

    private fun getPriceSpannableBuilder(price: Double): SpannableStringBuilder? {
        val priceTemp = StringUtil.DecimalFormat2Double(price)
        return StringUtil.getSpannableSizeWithDot(priceTemp, 18, 11)
    }
}